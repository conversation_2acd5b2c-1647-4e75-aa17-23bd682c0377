<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Dashboard;
use App\Livewire\EnhancedErrorAccounts;
use App\Livewire\ErrorAnalyticsDashboard;
use App\Http\Controllers\VpsController;
use App\Http\Controllers\PersonaController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\CommandController;

Route::get('/', function () {
    return view('welcome');
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', Dashboard::class)->name('dashboard');
    Route::get('/error-accounts', EnhancedErrorAccounts::class)->name('error-accounts');
    Route::get('/error-analytics', ErrorAnalyticsDashboard::class)->name('error-analytics');

    Route::get('/vps', [VpsController::class, 'index'])->name('vps.index');
    Route::get('/vps/create', [VpsController::class, 'create'])->name('vps.create');
    Route::post('/vps', [VpsController::class, 'store'])->name('vps.store');
    Route::get('/vps/{vps}', [VpsController::class, 'show'])->name('vps.show');
    Route::get('/vps/{vps}/edit', [VpsController::class, 'edit'])->name('vps.edit');
    Route::put('/vps/{vps}', [VpsController::class, 'update'])->name('vps.update');
    Route::delete('/vps/{vps}', [VpsController::class, 'destroy'])->name('vps.destroy');


    Route::get('/personas', [PersonaController::class, 'index'])->name('personas.index');
    Route::get('/personas/create', [PersonaController::class, 'create'])->name('personas.create');
    Route::post('/personas', [PersonaController::class, 'store'])->name('personas.store');
    Route::get('/personas/{persona}', [PersonaController::class, 'show'])->name('personas.show');
    Route::get('/personas/{persona}/edit', [PersonaController::class, 'edit'])->name('personas.edit');
    Route::put('/personas/{persona}', [PersonaController::class, 'update'])->name('personas.update');
    Route::delete('/personas/{persona}', [PersonaController::class, 'destroy'])->name('personas.destroy');
    Route::post('/personas/assign-vps', [PersonaController::class, 'assignVps'])->name('personas.assign-vps');


    Route::get('/accounts', [AccountController::class, 'index'])->name('accounts.index');
    Route::get('/accounts/create', [AccountController::class, 'create'])->name('accounts.create');
    Route::post('/accounts', [AccountController::class, 'store'])->name('accounts.store');
    Route::post('/accounts/{account}/login', [AccountController::class, 'login'])->name('accounts.login');
    Route::get('/accounts/{account}', [AccountController::class, 'show'])->name('accounts.show');
    Route::get('/accounts/{account}/edit', [AccountController::class, 'edit'])->name('accounts.edit');
    Route::put('/accounts/{account}', [AccountController::class, 'update'])->name('accounts.update');
    Route::delete('/accounts/{account}', [AccountController::class, 'destroy'])->name('accounts.destroy');
    Route::post('/accounts/assign-persona', [AccountController::class, 'assignPersona'])->name('accounts.assign-persona');

    Route::get('/commands/{command}/retry', [CommandController::class, 'retry'])->name('commands.retry');
    Route::get('/commands/{command}/logs', [CommandController::class, 'showLogs'])->name('commands.logs');
    Route::delete('/commands/{command}', [CommandController::class, 'destroy'])->name('commands.destroy');
});
