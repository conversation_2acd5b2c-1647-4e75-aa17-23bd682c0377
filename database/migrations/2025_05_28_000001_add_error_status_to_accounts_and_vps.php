<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->enum('current_error_status', ['none', 'warning', 'error', 'critical'])->default('none');
            $table->timestamp('last_error_at')->nullable();
        });

        Schema::table('vps', function (Blueprint $table) {
            $table->enum('current_error_status', ['none', 'warning', 'error', 'critical'])->default('none');
            $table->integer('consecutive_failures')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->dropColumn(['current_error_status', 'last_error_at']);
        });

        Schema::table('vps', function (Blueprint $table) {
            $table->dropColumn(['current_error_status', 'consecutive_failures']);
        });
    }
};
