<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('error_events', function (Blueprint $table) {
            $table->id();
            
            // Relations
            $table->foreignId('vps_id')->nullable()->constrained('vps')->onDelete('set null');
            $table->foreignId('account_id')->nullable()->constrained('accounts')->onDelete('set null');
            $table->foreignId('persona_id')->nullable()->constrained('personas')->onDelete('set null');
            $table->foreignId('command_id')->nullable()->constrained('commands')->onDelete('set null');
            
            // Error Classification
            $table->enum('error_category', [
                'authentication', 
                'communication', 
                'validation', 
                'system', 
                'browser', 
                'content_access'
            ]);
            $table->string('error_type', 100);
            $table->enum('severity', ['low', 'medium', 'high', 'critical']);
            
            // Error Details
            $table->text('error_message');
            $table->string('error_code', 20)->nullable();
            $table->json('error_context')->nullable();
            $table->string('screenshot_url')->nullable();
            
            // Source Information
            $table->enum('source_system', ['spore-vps', 'spore']);
            
            // Resolution Tracking
            $table->enum('status', ['open', 'resolved', 'ignored'])->default('open');
            $table->timestamp('resolved_at')->nullable();
            $table->boolean('auto_resolved')->default(false);
            
            // Metadata
            $table->timestamp('occurred_at');
            $table->timestamps();
            
            // Indexes
            $table->index(['account_id', 'status']);
            $table->index(['vps_id', 'occurred_at']);
            $table->index(['error_category', 'severity']);
            $table->index(['status', 'occurred_at']);
            $table->index(['source_system', 'error_category']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('error_events');
    }
};
