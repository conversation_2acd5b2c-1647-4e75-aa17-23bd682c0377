#!/bin/bash

echo "🧹 Clearing all Laravel caches..."

# Clear application cache
echo "Clearing application cache..."
php artisan cache:clear

# Clear configuration cache
echo "Clearing configuration cache..."
php artisan config:clear

# Clear route cache
echo "Clearing route cache..."
php artisan route:clear

# Clear view cache
echo "Clearing view cache..."
php artisan view:clear

# Clear compiled classes
echo "Clearing compiled classes..."
php artisan clear-compiled

# Optimize autoloader
echo "Optimizing autoloader..."
composer dump-autoload

# Clear Livewire component cache
echo "Clearing Livewire component cache..."
php artisan livewire:clear

echo "✅ All caches cleared successfully!"
echo ""
echo "🔄 Now rebuild your Docker containers and assets:"
echo "   docker-compose down && docker-compose up -d --build"
echo "   npm run build"
