<?php

namespace App\Livewire;

use Livewire\Component;
use App\Services\ErrorAnalyticsService;
use App\Services\ErrorTrackingService;
use Illuminate\Support\Facades\Cache;

class ErrorAnalyticsDashboard extends Component
{
    public string $timeRange = '24h';
    public string $chartType = 'trends';
    public array $errorTrends = [];
    public array $errorPatterns = [];
    public array $accountPatterns = [];
    public array $vpsPatterns = [];
    public array $anomalies = [];
    public bool $readyToLoad = false;

    protected ErrorAnalyticsService $analyticsService;
    protected ErrorTrackingService $errorTrackingService;

    public function boot(ErrorAnalyticsService $analyticsService, ErrorTrackingService $errorTrackingService)
    {
        $this->analyticsService = $analyticsService;
        $this->errorTrackingService = $errorTrackingService;
    }

    public function mount()
    {
        $this->readyToLoad = true;
        $this->loadAnalytics();
    }

    public function render()
    {
        return view('livewire.error-analytics-dashboard');
    }

    public function updatedTimeRange()
    {
        $this->loadAnalytics();
    }

    public function updatedChartType()
    {
        $this->loadAnalytics();
    }

    public function loadAnalytics()
    {
        $cacheKey = "error_analytics_{$this->timeRange}_{$this->chartType}";
        
        $data = Cache::remember($cacheKey, 300, function () {
            return [
                'trends' => $this->analyticsService->getErrorTrends($this->timeRange),
                'patterns' => $this->analyticsService->getErrorPatterns(),
                'accountPatterns' => $this->analyticsService->getAccountErrorPatterns(),
                'vpsPatterns' => $this->analyticsService->getVpsErrorPatterns(),
                'anomalies' => $this->analyticsService->detectAnomalies(),
            ];
        });

        $this->errorTrends = $data['trends'];
        $this->errorPatterns = $data['patterns'];
        $this->accountPatterns = $data['accountPatterns']->toArray();
        $this->vpsPatterns = $data['vpsPatterns']->toArray();
        $this->anomalies = $data['anomalies'];
    }

    public function refreshAnalytics()
    {
        // Clear relevant caches
        Cache::forget("error_analytics_{$this->timeRange}_{$this->chartType}");
        Cache::forget('error_patterns');
        Cache::forget('account_error_patterns');
        Cache::forget('vps_error_patterns');
        Cache::forget('error_anomalies');
        
        $this->loadAnalytics();
    }

    public function exportAnalytics()
    {
        $this->dispatch('export-analytics', [
            'timeRange' => $this->timeRange,
            'data' => [
                'trends' => $this->errorTrends,
                'patterns' => $this->errorPatterns,
                'accountPatterns' => $this->accountPatterns,
                'vpsPatterns' => $this->vpsPatterns,
                'anomalies' => $this->anomalies,
            ]
        ]);
    }

    public function getTimeRangeOptions()
    {
        return [
            '1h' => 'Last Hour',
            '24h' => 'Last 24 Hours',
            '7d' => 'Last 7 Days',
            '30d' => 'Last 30 Days'
        ];
    }

    public function getChartTypeOptions()
    {
        return [
            'trends' => 'Error Trends',
            'categories' => 'By Category',
            'severity' => 'By Severity',
            'vps' => 'By VPS'
        ];
    }

    public function getTrendChartData()
    {
        if (empty($this->errorTrends)) {
            return [];
        }

        $categories = ['authentication', 'communication', 'validation', 'system', 'browser', 'content_access'];
        $chartData = [
            'labels' => array_keys($this->errorTrends),
            'datasets' => []
        ];

        $colors = [
            'authentication' => '#ef4444',
            'communication' => '#f97316', 
            'validation' => '#eab308',
            'system' => '#8b5cf6',
            'browser' => '#06b6d4',
            'content_access' => '#10b981'
        ];

        foreach ($categories as $category) {
            $data = [];
            foreach ($this->errorTrends as $timePoint => $trends) {
                $categoryTotal = 0;
                if (isset($trends[$category])) {
                    foreach ($trends[$category] as $severity => $count) {
                        $categoryTotal += $count;
                    }
                }
                $data[] = $categoryTotal;
            }

            $chartData['datasets'][] = [
                'label' => ucfirst(str_replace('_', ' ', $category)),
                'data' => $data,
                'borderColor' => $colors[$category],
                'backgroundColor' => $colors[$category] . '20',
                'tension' => 0.4
            ];
        }

        return $chartData;
    }

    public function getSeverityDistributionData()
    {
        if (empty($this->errorPatterns['severity_distribution'])) {
            return [];
        }

        $distribution = $this->errorPatterns['severity_distribution'];
        
        return [
            'labels' => array_map('ucfirst', array_keys($distribution)),
            'data' => array_values($distribution),
            'backgroundColor' => [
                '#10b981', // low - green
                '#eab308', // medium - yellow
                '#f97316', // high - orange
                '#ef4444'  // critical - red
            ]
        ];
    }

    public function getTopErrorTypesData()
    {
        if (empty($this->errorPatterns['most_frequent_errors'])) {
            return [];
        }

        $errors = collect($this->errorPatterns['most_frequent_errors'])->take(10);
        
        return [
            'labels' => $errors->pluck('error_type')->map(function ($type) {
                return ucfirst(str_replace('_', ' ', $type));
            })->toArray(),
            'data' => $errors->pluck('count')->toArray(),
            'backgroundColor' => [
                '#ef4444', '#f97316', '#eab308', '#10b981', '#06b6d4',
                '#8b5cf6', '#ec4899', '#6b7280', '#f59e0b', '#3b82f6'
            ]
        ];
    }

    public function getResolutionMetrics()
    {
        if (empty($this->errorPatterns['resolution_rates'])) {
            return [
                'total' => 0,
                'resolved' => 0,
                'rate' => 0,
                'auto_resolved' => 0
            ];
        }

        return $this->errorPatterns['resolution_rates'];
    }

    public function getAnomalyAlerts()
    {
        return collect($this->anomalies)->map(function ($anomaly) {
            return [
                'type' => $anomaly['type'],
                'severity' => $anomaly['severity'],
                'message' => $anomaly['message'],
                'data' => $anomaly['data'],
                'color' => match($anomaly['severity']) {
                    'critical' => 'red',
                    'high' => 'orange',
                    'medium' => 'yellow',
                    default => 'blue'
                }
            ];
        })->toArray();
    }

    public function getTopProblematicAccounts()
    {
        return collect($this->accountPatterns)
            ->sortByDesc('critical_errors')
            ->take(5)
            ->map(function ($account) {
                return [
                    'id' => $account['id'],
                    'name' => $account['login_name'] ?? 'Unknown',
                    'email' => $account['registered_email'] ?? 'No email',
                    'total_errors' => $account['total_errors'],
                    'critical_errors' => $account['critical_errors'],
                    'open_errors' => $account['open_errors'],
                    'error_categories' => $account['error_categories'],
                    'last_error_at' => $account['last_error_at'],
                    'severity_color' => $account['critical_errors'] > 0 ? 'red' : 
                                      ($account['high_errors'] > 0 ? 'orange' : 'yellow')
                ];
            })
            ->values()
            ->toArray();
    }

    public function getTopProblematicVps()
    {
        return collect($this->vpsPatterns)
            ->sortByDesc('critical_errors')
            ->take(5)
            ->map(function ($vps) {
                return [
                    'id' => $vps['id'],
                    'name' => $vps['name'],
                    'ip' => $vps['external_ip'],
                    'total_errors' => $vps['total_errors'],
                    'critical_errors' => $vps['critical_errors'],
                    'communication_errors' => $vps['communication_errors'],
                    'affected_accounts' => $vps['affected_accounts'],
                    'avg_resolution_time' => $vps['avg_resolution_time'] ? round($vps['avg_resolution_time'], 1) : null,
                    'last_error_at' => $vps['last_error_at'],
                    'severity_color' => $vps['critical_errors'] > 0 ? 'red' : 
                                      ($vps['communication_errors'] > 5 ? 'orange' : 'yellow')
                ];
            })
            ->values()
            ->toArray();
    }

    public function viewAccountDetails($accountId)
    {
        $this->dispatch('view-account-details', ['accountId' => $accountId]);
    }

    public function viewVpsDetails($vpsId)
    {
        $this->dispatch('view-vps-details', ['vpsId' => $vpsId]);
    }

    public function filterErrorsByAccount($accountId)
    {
        $this->dispatch('filter-errors-by-account', ['accountId' => $accountId]);
    }

    public function filterErrorsByVps($vpsId)
    {
        $this->dispatch('filter-errors-by-vps', ['vpsId' => $vpsId]);
    }
}
