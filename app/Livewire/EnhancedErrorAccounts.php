<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Services\ErrorTrackingService;
use App\Models\ErrorEvent;
use App\Models\Vps;
use Illuminate\Support\Facades\Cache;

class EnhancedErrorAccounts extends Component
{
    use WithPagination;

    // Filtering properties
    public string $filterCategory = 'all';
    public string $filterSeverity = 'all';
    public string $filterVpsId = 'all';
    public string $timeRange = '24h';
    public string $search = '';
    
    // Grouping and sorting
    public string $groupBy = 'category';
    public string $sortBy = 'severity';
    public string $sortDirection = 'desc';
    
    // UI state
    public bool $showFilters = false;
    public array $selectedAccounts = [];
    public bool $readyToLoad = false;

    protected ErrorTrackingService $errorTrackingService;

    public function boot(ErrorTrackingService $errorTrackingService)
    {
        $this->errorTrackingService = $errorTrackingService;
    }

    public function mount()
    {
        $this->readyToLoad = true;
    }

    public function render()
    {
        if (!$this->readyToLoad) {
            return view('livewire.enhanced-error-accounts-loading');
        }

        $cacheKey = 'enhanced_error_accounts_' . md5(json_encode([
            'category' => $this->filterCategory,
            'severity' => $this->filterSeverity,
            'vps_id' => $this->filterVpsId,
            'time_range' => $this->timeRange,
            'search' => $this->search,
            'page' => $this->getPage()
        ]));

        $data = Cache::remember($cacheKey, 60, function () {
            return [
                'errorAccounts' => $this->getErrorAccounts(),
                'errorSummary' => $this->getErrorSummary(),
                'vpsList' => $this->getVpsList()
            ];
        });

        return view('livewire.enhanced-error-accounts', $data);
    }

    public function getErrorAccounts()
    {
        $filters = [
            'error_category' => $this->filterCategory,
            'severity' => $this->filterSeverity,
            'vps_id' => $this->filterVpsId !== 'all' ? (int)$this->filterVpsId : null,
            'time_range' => $this->timeRange
        ];

        $accounts = $this->errorTrackingService->getErrorAccounts($filters);

        // Apply search filter
        if (!empty($this->search)) {
            $accounts = $accounts->filter(function ($account) {
                return str_contains(strtolower($account->login_name ?? ''), strtolower($this->search)) ||
                       str_contains(strtolower($account->registered_email ?? ''), strtolower($this->search)) ||
                       str_contains(strtolower($account->latest_error->error_message ?? ''), strtolower($this->search));
            });
        }

        // Apply grouping
        if ($this->groupBy === 'category') {
            return $accounts->groupBy(function ($account) {
                return $account->latest_error->error_category ?? 'unknown';
            });
        } elseif ($this->groupBy === 'severity') {
            return $accounts->groupBy(function ($account) {
                return $account->highest_severity ?? 'unknown';
            });
        } elseif ($this->groupBy === 'vps') {
            return $accounts->groupBy(function ($account) {
                return $account->latest_error->vps->name ?? 'Unknown VPS';
            });
        }

        return collect(['all' => $accounts]);
    }

    public function getErrorSummary()
    {
        return $this->errorTrackingService->getErrorMetrics($this->timeRange);
    }

    public function getVpsList()
    {
        return Vps::select('id', 'name', 'external_ip')
            ->orderBy('name')
            ->get();
    }

    public function updatedFilterCategory()
    {
        $this->resetPage();
        $this->clearCache();
    }

    public function updatedFilterSeverity()
    {
        $this->resetPage();
        $this->clearCache();
    }

    public function updatedFilterVpsId()
    {
        $this->resetPage();
        $this->clearCache();
    }

    public function updatedTimeRange()
    {
        $this->resetPage();
        $this->clearCache();
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->clearCache();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function resetFilters()
    {
        $this->filterCategory = 'all';
        $this->filterSeverity = 'all';
        $this->filterVpsId = 'all';
        $this->timeRange = '24h';
        $this->search = '';
        $this->groupBy = 'category';
        $this->selectedAccounts = [];
        $this->resetPage();
        $this->clearCache();
    }

    public function resolveError($errorEventId)
    {
        $errorEvent = ErrorEvent::find($errorEventId);
        if ($errorEvent) {
            $this->errorTrackingService->resolveError($errorEvent, 'Manually resolved by administrator');
            $this->clearCache();
            $this->dispatch('error-resolved', ['errorEventId' => $errorEventId]);
        }
    }

    public function retryAccountLogin($accountId)
    {
        // This would trigger a new login command for the account
        // Implementation depends on your command creation logic
        $this->dispatch('retry-account-login', ['accountId' => $accountId]);
    }

    public function bulkResolveErrors()
    {
        if (!empty($this->selectedAccounts)) {
            foreach ($this->selectedAccounts as $accountId) {
                $errorEvents = ErrorEvent::where('account_id', $accountId)
                    ->where('status', ErrorEvent::STATUS_OPEN)
                    ->get();
                
                foreach ($errorEvents as $errorEvent) {
                    $this->errorTrackingService->resolveError($errorEvent, 'Bulk resolved by administrator');
                }
            }
            
            $this->selectedAccounts = [];
            $this->clearCache();
            $this->dispatch('bulk-errors-resolved');
        }
    }

    public function exportErrorReport()
    {
        // This would generate and download an error report
        $this->dispatch('export-error-report', [
            'filters' => [
                'category' => $this->filterCategory,
                'severity' => $this->filterSeverity,
                'vps_id' => $this->filterVpsId,
                'time_range' => $this->timeRange
            ]
        ]);
    }

    private function clearCache()
    {
        Cache::forget('enhanced_error_accounts_*');
    }

    public function getErrorCategoryOptions()
    {
        return [
            'all' => 'All Categories',
            ErrorEvent::CATEGORY_AUTHENTICATION => 'Authentication',
            ErrorEvent::CATEGORY_COMMUNICATION => 'Communication',
            ErrorEvent::CATEGORY_VALIDATION => 'Validation',
            ErrorEvent::CATEGORY_SYSTEM => 'System',
            ErrorEvent::CATEGORY_BROWSER => 'Browser',
            ErrorEvent::CATEGORY_CONTENT_ACCESS => 'Content Access'
        ];
    }

    public function getSeverityOptions()
    {
        return [
            'all' => 'All Severities',
            ErrorEvent::SEVERITY_CRITICAL => 'Critical',
            ErrorEvent::SEVERITY_HIGH => 'High',
            ErrorEvent::SEVERITY_MEDIUM => 'Medium',
            ErrorEvent::SEVERITY_LOW => 'Low'
        ];
    }

    public function getTimeRangeOptions()
    {
        return [
            '1h' => 'Last Hour',
            '24h' => 'Last 24 Hours',
            '7d' => 'Last 7 Days',
            '30d' => 'Last 30 Days'
        ];
    }

    public function getGroupByOptions()
    {
        return [
            'category' => 'Error Category',
            'severity' => 'Severity Level',
            'vps' => 'VPS Server',
            'none' => 'No Grouping'
        ];
    }
}
