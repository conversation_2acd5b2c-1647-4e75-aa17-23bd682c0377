<?php

namespace App\Livewire;

use Livewire\Component;
use App\Services\ErrorAnalyticsService;
use App\Models\ErrorEvent;
use Illuminate\Support\Facades\Cache;

class ErrorAlertsComponent extends Component
{
    public array $alerts = [];
    public array $anomalies = [];
    public bool $showAlerts = true;
    public int $alertCount = 0;
    public string $alertLevel = 'none'; // none, warning, critical

    protected ErrorAnalyticsService $analyticsService;

    public function boot(ErrorAnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    public function mount()
    {
        $this->loadAlerts();
    }

    public function render()
    {
        return view('livewire.error-alerts-component');
    }

    public function loadAlerts()
    {
        // Get current anomalies
        $this->anomalies = $this->analyticsService->detectAnomalies();
        
        // Generate UI alerts based on current error state
        $this->alerts = $this->generateAlerts();
        
        // Update alert count and level
        $this->alertCount = count($this->alerts);
        $this->alertLevel = $this->determineAlertLevel();
    }

    public function toggleAlerts()
    {
        $this->showAlerts = !$this->showAlerts;
    }

    public function dismissAlert($alertIndex)
    {
        if (isset($this->alerts[$alertIndex])) {
            // Store dismissed alert in cache to prevent re-showing for a while
            $alert = $this->alerts[$alertIndex];
            $cacheKey = 'dismissed_alert_' . md5(json_encode($alert));
            Cache::put($cacheKey, true, now()->addHours(2));
            
            unset($this->alerts[$alertIndex]);
            $this->alerts = array_values($this->alerts); // Re-index array
            $this->alertCount = count($this->alerts);
            $this->alertLevel = $this->determineAlertLevel();
        }
    }

    public function refreshAlerts()
    {
        // Clear cache and reload
        Cache::forget('error_anomalies');
        $this->loadAlerts();
    }

    private function generateAlerts(): array
    {
        $alerts = [];

        // Add anomaly-based alerts
        foreach ($this->anomalies as $anomaly) {
            $cacheKey = 'dismissed_alert_' . md5(json_encode($anomaly));
            if (!Cache::has($cacheKey)) {
                $alerts[] = [
                    'type' => 'anomaly',
                    'severity' => $anomaly['severity'],
                    'title' => $this->getAnomalyTitle($anomaly['type']),
                    'message' => $anomaly['message'],
                    'data' => $anomaly['data'],
                    'timestamp' => now(),
                    'actions' => $this->getAnomalyActions($anomaly['type'])
                ];
            }
        }

        // Check for critical errors in last 10 minutes
        $recentCritical = ErrorEvent::where('severity', ErrorEvent::SEVERITY_CRITICAL)
            ->where('status', ErrorEvent::STATUS_OPEN)
            ->where('occurred_at', '>=', now()->subMinutes(10))
            ->with(['account', 'vps'])
            ->get();

        foreach ($recentCritical as $error) {
            $cacheKey = 'dismissed_alert_critical_' . $error->id;
            if (!Cache::has($cacheKey)) {
                $alerts[] = [
                    'type' => 'critical_error',
                    'severity' => 'critical',
                    'title' => 'Critical Error Detected',
                    'message' => "Critical error on account {$error->account->login_name ?? 'Unknown'}: {$error->error_message}",
                    'data' => [
                        'error_id' => $error->id,
                        'account_id' => $error->account_id,
                        'vps_id' => $error->vps_id,
                        'error_type' => $error->error_type
                    ],
                    'timestamp' => $error->occurred_at,
                    'actions' => [
                        ['label' => 'View Details', 'action' => 'viewError', 'params' => ['id' => $error->id]],
                        ['label' => 'Resolve', 'action' => 'resolveError', 'params' => ['id' => $error->id]]
                    ]
                ];
            }
        }

        // Check for VPS communication issues
        $vpsIssues = ErrorEvent::where('error_category', ErrorEvent::CATEGORY_COMMUNICATION)
            ->where('occurred_at', '>=', now()->subMinutes(15))
            ->where('status', ErrorEvent::STATUS_OPEN)
            ->with('vps')
            ->get()
            ->groupBy('vps_id');

        foreach ($vpsIssues as $vpsId => $errors) {
            if ($errors->count() >= 3) {
                $vps = $errors->first()->vps;
                $cacheKey = 'dismissed_alert_vps_' . $vpsId . '_' . now()->format('Y-m-d-H');
                if (!Cache::has($cacheKey)) {
                    $alerts[] = [
                        'type' => 'vps_issues',
                        'severity' => 'high',
                        'title' => 'VPS Communication Issues',
                        'message' => "VPS {$vps->name} has {$errors->count()} communication errors in the last 15 minutes",
                        'data' => [
                            'vps_id' => $vpsId,
                            'error_count' => $errors->count(),
                            'vps_name' => $vps->name
                        ],
                        'timestamp' => $errors->max('occurred_at'),
                        'actions' => [
                            ['label' => 'Check VPS', 'action' => 'viewVps', 'params' => ['id' => $vpsId]],
                            ['label' => 'View Errors', 'action' => 'filterByVps', 'params' => ['vps_id' => $vpsId]]
                        ]
                    ];
                }
            }
        }

        // Check for accounts with multiple recent errors
        $problematicAccounts = ErrorEvent::where('occurred_at', '>=', now()->subHour())
            ->where('status', ErrorEvent::STATUS_OPEN)
            ->with('account')
            ->get()
            ->groupBy('account_id')
            ->filter(function ($errors) {
                return $errors->count() >= 5;
            });

        foreach ($problematicAccounts as $accountId => $errors) {
            $account = $errors->first()->account;
            $cacheKey = 'dismissed_alert_account_' . $accountId . '_' . now()->format('Y-m-d-H');
            if (!Cache::has($cacheKey)) {
                $alerts[] = [
                    'type' => 'problematic_account',
                    'severity' => 'medium',
                    'title' => 'Account with Multiple Errors',
                    'message' => "Account {$account->login_name ?? 'Unknown'} has {$errors->count()} errors in the last hour",
                    'data' => [
                        'account_id' => $accountId,
                        'error_count' => $errors->count(),
                        'account_name' => $account->login_name
                    ],
                    'timestamp' => $errors->max('occurred_at'),
                    'actions' => [
                        ['label' => 'View Account', 'action' => 'viewAccount', 'params' => ['id' => $accountId]],
                        ['label' => 'Filter Errors', 'action' => 'filterByAccount', 'params' => ['account_id' => $accountId]]
                    ]
                ];
            }
        }

        // Sort alerts by severity and timestamp
        usort($alerts, function ($a, $b) {
            $severityOrder = ['critical' => 4, 'high' => 3, 'medium' => 2, 'low' => 1];
            $aSeverity = $severityOrder[$a['severity']] ?? 0;
            $bSeverity = $severityOrder[$b['severity']] ?? 0;
            
            if ($aSeverity === $bSeverity) {
                return $b['timestamp'] <=> $a['timestamp'];
            }
            return $bSeverity <=> $aSeverity;
        });

        return array_slice($alerts, 0, 10); // Limit to 10 alerts
    }

    private function determineAlertLevel(): string
    {
        if (empty($this->alerts)) {
            return 'none';
        }

        foreach ($this->alerts as $alert) {
            if ($alert['severity'] === 'critical') {
                return 'critical';
            }
        }

        foreach ($this->alerts as $alert) {
            if (in_array($alert['severity'], ['high', 'medium'])) {
                return 'warning';
            }
        }

        return 'warning';
    }

    private function getAnomalyTitle(string $type): string
    {
        return match($type) {
            'error_spike' => 'Error Spike Detected',
            'critical_errors' => 'Multiple Critical Errors',
            'vps_failures' => 'VPS Communication Failures',
            default => 'System Anomaly Detected'
        };
    }

    private function getAnomalyActions(string $type): array
    {
        return match($type) {
            'error_spike' => [
                ['label' => 'View Recent Errors', 'action' => 'viewRecentErrors'],
                ['label' => 'Check System Status', 'action' => 'checkSystemStatus']
            ],
            'critical_errors' => [
                ['label' => 'View Critical Errors', 'action' => 'filterBySeverity', 'params' => ['severity' => 'critical']],
                ['label' => 'Emergency Response', 'action' => 'emergencyResponse']
            ],
            'vps_failures' => [
                ['label' => 'Check VPS Status', 'action' => 'checkVpsStatus'],
                ['label' => 'View Communication Errors', 'action' => 'filterByCategory', 'params' => ['category' => 'communication']]
            ],
            default => [
                ['label' => 'Investigate', 'action' => 'investigate']
            ]
        };
    }

    // Action handlers (these would dispatch events to parent components)
    public function viewError($errorId)
    {
        $this->dispatch('view-error', ['errorId' => $errorId]);
    }

    public function resolveError($errorId)
    {
        $this->dispatch('resolve-error', ['errorId' => $errorId]);
    }

    public function viewVps($vpsId)
    {
        $this->dispatch('view-vps', ['vpsId' => $vpsId]);
    }

    public function filterByVps($vpsId)
    {
        $this->dispatch('filter-by-vps', ['vpsId' => $vpsId]);
    }

    public function filterBySeverity($severity)
    {
        $this->dispatch('filter-by-severity', ['severity' => $severity]);
    }

    public function filterByCategory($category)
    {
        $this->dispatch('filter-by-category', ['category' => $category]);
    }
}
