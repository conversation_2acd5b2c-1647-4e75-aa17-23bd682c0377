<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ErrorEvent extends Model
{
    // Error Categories
    const CATEGORY_AUTHENTICATION = 'authentication';
    const CATEGORY_COMMUNICATION = 'communication';
    const CATEGORY_VALIDATION = 'validation';
    const CATEGORY_SYSTEM = 'system';
    const CATEGORY_BROWSER = 'browser';
    const CATEGORY_CONTENT_ACCESS = 'content_access';
    
    // Error Types
    const TYPE_WRONG_PASSWORD = 'wrong_password';
    const TYPE_NOT_LOGGED_IN = 'not_logged_in';
    const TYPE_ACCOUNT_SUSPENDED = 'account_suspended';
    const TYPE_TWO_FACTOR_REQUIRED = 'two_factor_required';
    const TYPE_VPS_TIMEOUT = 'vps_timeout';
    const TYPE_VPS_UNREACHABLE = 'vps_unreachable';
    const TYPE_TWITTER_CONNECTION_FAILED = 'twitter_connection_failed';
    const TYPE_MISSING_PARAMETERS = 'missing_parameters';
    const TYPE_INVALID_CREDENTIALS = 'invalid_credentials';
    const TYPE_MISSING_PERSONA = 'missing_persona';
    const TYPE_BROWSER_LAUNCH_FAILED = 'browser_launch_failed';
    const TYPE_DATABASE_ERROR = 'database_error';
    const TYPE_QUEUE_FAILURE = 'queue_failure';
    const TYPE_FORM_ACCESS_FAILED = 'form_access_failed';
    const TYPE_INPUT_FAILED = 'input_failed';
    const TYPE_BUTTON_CLICK_FAILED = 'button_click_failed';
    const TYPE_STATUS_NOT_FOUND = 'status_not_found';
    const TYPE_NAVIGATION_FAILED = 'navigation_failed';
    const TYPE_POST_SUBMISSION_FAILED = 'post_submission_failed';
    const TYPE_RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded';
    
    // Severity Levels
    const SEVERITY_LOW = 'low';
    const SEVERITY_MEDIUM = 'medium';
    const SEVERITY_HIGH = 'high';
    const SEVERITY_CRITICAL = 'critical';
    
    // Status Values
    const STATUS_OPEN = 'open';
    const STATUS_RESOLVED = 'resolved';
    const STATUS_IGNORED = 'ignored';

    protected $fillable = [
        'vps_id', 'account_id', 'persona_id', 'command_id',
        'error_category', 'error_type', 'severity',
        'error_message', 'error_code', 'error_context', 'screenshot_url',
        'source_system',
        'status', 'resolved_at', 'auto_resolved',
        'occurred_at'
    ];

    protected $casts = [
        'error_context' => 'array',
        'auto_resolved' => 'boolean',
        'occurred_at' => 'datetime',
        'resolved_at' => 'datetime',
    ];

    // Relationships
    public function vps(): BelongsTo
    {
        return $this->belongsTo(Vps::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function persona(): BelongsTo
    {
        return $this->belongsTo(Persona::class);
    }

    public function command(): BelongsTo
    {
        return $this->belongsTo(Command::class);
    }

    // Scopes
    public function scopeOpen($query)
    {
        return $query->where('status', self::STATUS_OPEN);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('error_category', $category);
    }

    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    public function scopeInTimeRange($query, string $timeRange)
    {
        $startTime = match($timeRange) {
            '1h' => now()->subHour(),
            '24h' => now()->subDay(),
            '7d' => now()->subWeek(),
            '30d' => now()->subMonth(),
            default => now()->subDay()
        };

        return $query->where('occurred_at', '>=', $startTime);
    }

    public function scopeForAccount($query, int $accountId)
    {
        return $query->where('account_id', $accountId);
    }

    public function scopeForVps($query, int $vpsId)
    {
        return $query->where('vps_id', $vpsId);
    }
}
