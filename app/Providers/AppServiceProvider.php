<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\ErrorTrackingService;
use App\Services\ErrorAnalyticsService;
use App\Services\LoggingService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(ErrorTrackingService::class, function ($app) {
            return new ErrorTrackingService($app->make(LoggingService::class));
        });

        $this->app->singleton(ErrorAnalyticsService::class, function ($app) {
            return new ErrorAnalyticsService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
