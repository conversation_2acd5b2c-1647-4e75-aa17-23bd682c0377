<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CommandMetricsService;
use App\Models\Command as CommandModel;
use Illuminate\Support\Facades\Log;

class GenerateCommandMetrics extends Command
{
    protected $signature = 'commands:generate-metrics {--debug : Show debug information}';
    protected $description = 'Manually generate command metrics';

    public function handle(CommandMetricsService $metricsService)
    {
        $this->info('Starting metrics generation...');
        
        // Get all commands
        $commands = CommandModel::orderBy('id')
            ->get();
            
        $this->info("Found {$commands->count()} commands");
        
        $processed = 0;
        foreach ($commands as $command) {
            try {
                if ($this->option('debug')) {
                    $this->line("Processing command {$command->id}");
                }
                
                $metricsService->recordCompletion($command);
                $processed++;
                
                if ($this->option('debug')) {
                    $this->line("Successfully processed command {$command->id}");
                }
            } catch (\Exception $e) {
                $this->error("Error processing command {$command->id}: " . $e->getMessage());
                Log::error("Error generating metrics for command {$command->id}", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
        
        $this->info("Successfully processed {$processed} commands");
        
        // Run aggregation
        $this->info('Running metrics aggregation...');
        try {
            $metricsService->aggregateMetrics();
            $this->info('Metrics aggregation completed successfully');
        } catch (\Exception $e) {
            $this->error('Error during metrics aggregation: ' . $e->getMessage());
            Log::error('Error during metrics aggregation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
} 