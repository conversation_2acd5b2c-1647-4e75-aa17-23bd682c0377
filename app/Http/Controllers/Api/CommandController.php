<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\StoreCommandRequest;
use App\Models\Command;
use App\Models\Persona;
use App\Models\Account;
use App\Models\Vps;
use App\Jobs\ProcessCommand;
use Illuminate\Support\Facades\Log;
use App\Services\Twitter\TwitterUserLookupService;
use App\Services\LoggingService;
use App\Helpers\CountryHelper;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CommandController extends Controller
{
    protected LoggingService $loggingService;

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    // endpoint to receive commands
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        Log::info('Command received', $request->all());
        $persona = null;
        $account = null;
        $persona = Persona::where('user_id', $request->persona_id)->first();
        if (!$persona) {
            // if persona is not found, persona_id in command might correspont to an actual accounts.account_id
            Log::warning('Persona not found, looking up account', ['user_id' => $request->persona_id]);
            $account = Account::where('account_type', $request->target_network)->where('account_id', $request->persona_id)->first();
            if (!$account) {
                Log::warning('Neither Persona, nor Account found', ['persona_id' => $request->persona_id, 'target_network' => $request->target_network, 'request' => $request->all()]);

                // if login info is provided (login_handle, login_password, registered_email, registered_phone, country), create a persona and an account
                if ($request->login_handle && $request->login_password) {
                    // get user_id from login_handle via lookupUserByName in TwitterUserLookupService
                    $twitterUserLookupService = new TwitterUserLookupService($this->loggingService);
                    $lookupResult = $twitterUserLookupService->lookupUser($request->persona_id);

                    if (!$lookupResult['success']) {
                        Log::warning('Twitter User lookup failed', ['lookup_result' => $lookupResult, 'request' => $request->all()]);
                        return response()->json([
                            'success' => false,
                            'error' => [
                                'status' => 'error',
                                'statusCode' => 400,
                                'message' => 'Twitter User lookup failed',
                                'data' => [
                                    'persona_id' => $request->persona_id,
                                    'target_network' => $request->target_network
                                ]
                            ],
                            'logs' => $this->loggingService->getRecentLogs()
                        ], 400);
                    }

                    $userData = $lookupResult['data'];
                    Log::info('Twitter user found', ['user_data' => $userData]);
                    $persona = Persona::updateOrCreate(
                        ['user_id' => $userData['id']],
                        [
                            'first_name' => $userData['first_name'],
                            'last_name' => $userData['last_name'],
                            'country' => $request->country,
                            'phone' => $request->registered_phone,
                            'email' => $request->registered_email
                        ]
                    );
                    Log::info('New Persona created', ['persona_id' => $persona->id]);

                    // if country is provided (2 letter code), select a random vps from that country
                    if ($request->country && $persona->vps_id === null) {
                        $vps = Vps::where('country', CountryHelper::codeToName($request->country))->inRandomOrder()->lockForUpdate()->first();
                        if (!$vps) {
                            throw new \Exception('No VPS found for country');
                        }
                        $persona->vps_id = $vps->id;
                        $persona->save();
                    }
                    Log::info('Persona vps set', ['persona_id' => $persona->id, 'vps_id' => $persona->vps_id]);

                    // Prepare account data and update only if provided
                    $accountData = [];
                    if (!empty($request->login_handle)) {
                        $accountData['login_name'] = $request->login_handle;
                    }
                    if (!empty($request->login_password)) {
                        $accountData['login_password'] = $request->login_password;
                    }
                    if (!empty($request->registered_email)) {
                        $accountData['registered_email'] = $request->registered_email;
                    }
                    if (!empty($request->registered_phone)) {
                        $accountData['registered_phone'] = $request->registered_phone;
                    }
                    if (!empty($request->two_factor_secret) || !empty($request->input('2fa_code'))) {
                        $accountData['two_factor_secret'] = $request->two_factor_secret ?? $request->input('2fa_code');
                    }
                    $accountData['account_type'] = $request->target_network;
                    $accountData['display_name'] = $userData['display_name'];
                    $accountData['followers_count'] = $userData['followers_count'];
                    $accountData['following_count'] = $userData['following_count'];
                    $accountData['posts_count'] = $userData['tweets_count'];

                    $account = Account::updateOrCreate(
                        ['persona_id' => $persona->id, 'account_id' => $userData['id']],
                        $accountData
                    );
                    Log::info('New Account created/updated', ['account_id' => $account->id]);

                    $requestId = (string) Str::uuid();
                    $command = Command::create([
                        'source_ip' => $request->ip(),
                        'persona_id' => $persona->id,
                        'account_id' => $account->id,
                        'user_id' => $account->account_id,
                        'command_params' => array_merge($request->command_params ?? [], ['original_persona_id' => $request->persona_id]),
                        'target_network' => $request->target_network ?? 'twitter',
                        'type' => 'login',
                        'message' => $request->message,
                        'reply_to' => $request->reply_to ?? null,
                        'status' => 'open',
                        'request_id' => $requestId,
                        'two_factor_secret' => $request->two_factor_secret ?? $request->input('2fa_code')
                    ]);
                    ProcessCommand::dispatch($command);
                } else {
                    return response()->json([
                        'success' => false,
                        'error' => [
                            'status' => 'error',
                            'statusCode' => 400,
                            'message' => 'Missing login credentials',
                            'data' => [
                                'persona_id' => $request->persona_id,
                                'target_network' => $request->target_network
                            ]
                        ],
                        'logs' => $this->loggingService->getRecentLogs()
                    ], 400);
                }
            } else {
                $persona_id = $account->persona_id;
                $persona = Persona::find($persona_id);
                Log::info('Account found:', ['account_id' => $account->id, 'persona_id' => $persona->id]);
            }
        } else {
            Log::info('Persona found', ['persona_id' => $persona->id]);
            $account = Persona::find($persona->id)->accounts()->where('account_type', $request->target_network)->first();
            if (!$account) {
                Log::warning('Account not found', ['persona_id' => $persona->id, 'target_network' => $request->target_network]);

                // Check if login credentials are provided to create a new account
                if ($request->login_handle && $request->login_password) {
                    Log::info('Creating new account for existing persona', ['persona_id' => $persona->id, 'target_network' => $request->target_network]);

                    // Prepare account data
                    $accountData = [];
                    if (!empty($request->login_handle)) {
                        $accountData['login_name'] = $request->login_handle;
                    }
                    if (!empty($request->login_password)) {
                        $accountData['login_password'] = $request->login_password;
                    }
                    if (!empty($request->registered_email)) {
                        $accountData['registered_email'] = $request->registered_email;
                    }
                    if (!empty($request->registered_phone)) {
                        $accountData['registered_phone'] = $request->registered_phone;
                    }
                    if (!empty($request->two_factor_secret) || !empty($request->input('2fa_code'))) {
                        $accountData['two_factor_secret'] = $request->two_factor_secret ?? $request->input('2fa_code');
                    }
                    $accountData['account_type'] = $request->target_network;
                    $accountData['persona_id'] = $persona->id;
                    $accountData['account_id'] = $request->persona_id; // Use the provided persona_id as account_id

                    // Create the account
                    $account = Account::create($accountData);
                    Log::info('New Account created for existing persona', ['account_id' => $account->id]);

                    // Create and dispatch login command
                    $loginRequestId = (string) Str::uuid();
                    $loginCommand = Command::create([
                        'source_ip' => $request->ip(),
                        'persona_id' => $persona->id,
                        'account_id' => $account->id,
                        'user_id' => $account->account_id,
                        'command_params' => array_merge($request->command_params ?? [], [
                            'original_persona_id' => $request->persona_id,
                            'original_command_type' => $request->type,
                            'original_message' => $request->message,
                            'original_reply_to' => $request->reply_to ?? null
                        ]),
                        'target_network' => $request->target_network ?? 'twitter',
                        'type' => 'login',
                        'status' => 'open',
                        'request_id' => $loginRequestId,
                        'two_factor_secret' => $request->two_factor_secret ?? $request->input('2fa_code')
                    ]);
                    ProcessCommand::dispatch($loginCommand);

                    // Create and dispatch the original command
                    $requestId = (string) Str::uuid();
                    $command = Command::create([
                        'source_ip' => $request->ip(),
                        'persona_id' => $persona->id,
                        'account_id' => $account->id,
                        'user_id' => $account->account_id,
                        'command_params' => array_merge($request->command_params ?? [], ['original_persona_id' => $request->persona_id]),
                        'target_network' => $request->target_network ?? 'twitter',
                        'type' => $request->type,
                        'message' => $request->message,
                        'reply_to' => $request->reply_to ?? null,
                        'status' => 'open',
                        'request_id' => $requestId,
                        'two_factor_secret' => $request->two_factor_secret ?? $request->input('2fa_code')
                    ]);

                    // Don't dispatch the original command yet - it will be processed after login
                    // We'll set it to pending so it's not picked up by the queue yet
                    $command->status = 'pending';
                    $command->save();

                    Log::info('Original command created and set to pending', ['command_id' => $command->id]);
                } else {
                    return $this->errorResponse('error', 404, 'Account not found for persona and no login credentials provided', ['persona_id' => $persona->id, 'target_network' => $request->target_network], 404);
                }
            } else {
                Log::info('Account found', ['account_id' => $account->id]);

                // Check for duplicate command regardless of status
                $existingCommand = Command::where('account_id', $account->id)
                    ->where('message', $request->message)
                    ->first();
                if ($existingCommand) {
                    // Check if account parameters have changed
                    if ($this->hasAccountParametersChanged($request, $account)) {
                        // Update account with new parameters
                        $this->updateAccountFromRequest($request, $account);

                        // Cancel any pending commands with same message to avoid conflicts
                        $this->cancelPendingCommands($account->id, $request->message, $existingCommand->id);

                        Log::info('Account credentials updated, allowing new command for duplicate message', [
                            'existing_command_id' => $existingCommand->id,
                            'account_id' => $account->id,
                            'message' => $request->message
                        ]);

                        // Continue to create new command
                    } else {
                        Log::warning('Duplicate command with no parameter changes', ['command_id' => $existingCommand->id, 'account_id' => $account->id]);
                        return response()->json([
                            'success' => false,
                            'error' => [
                                'status' => 'error',
                                'statusCode' => 400,
                                'message' => 'Duplicate command',
                                'data' => [
                                    'persona_id' => $request->persona_id,
                                    'target_network' => $request->target_network
                                ]
                            ],
                            'logs' => $this->loggingService->getRecentLogs()
                        ], 400);
                    }
                }
                $requestId = (string) Str::uuid();
                $command = Command::create([
                    'source_ip' => $request->ip(),
                    'persona_id' => $persona->id,
                    'account_id' => $account->id,
                    'user_id' => $account->account_id,
                    'command_params' => array_merge($request->command_params ?? [], ['original_persona_id' => $request->persona_id]),
                    'target_network' => $request->target_network ?? 'twitter',
                    'type' => $request->type,
                    'message' => $request->message,
                    'reply_to' => $request->reply_to ?? null,
                    'status' => 'open',
                    'request_id' => $requestId,
                    'two_factor_secret' => $request->two_factor_secret ?? $request->input('2fa_code') // TODO: phase out 2fa_code key name
                ]);
                ProcessCommand::dispatch($command);
                $command->status = 'pending';
                $command->save();
            }
        }

        Log::info('Command created and dispatched', ['command_id' => $command->id, 'request_id' => $command->request_id]);

        return response()->json([
            'request_id' => $command->request_id
        ], 200);
    }

    private function errorResponse($status, $statusCode, $message, $data, $responseCode)
    {
        Log::error('Error response', ['status' => $status, 'statusCode' => $statusCode, 'message' => $message, 'data' => $data, 'responseCode' => $responseCode]);
        return response()->json([
            'success' => false,
            'error' => [
                'status' => $status,
                'statusCode' => $statusCode,
                'message' => $message,
                'data' => $data
            ],
            'logs' => $this->loggingService->getRecentLogs()
        ], $responseCode);
    }

    /**
     * Get the status of a command by its request_id
     */
    public function getStatus(string $request_id)
    {
        $command = Command::where('request_id', $request_id)->first();

        if (!$command) {
            return response()->json(['error' => 'Not Found'], 404);
        }

        switch ($command->status) {
            case 'open':
            case 'pending':
                return response()->json(['status' => 'pending']);

            case 'success': // Assuming 'success' might be used sometimes, treat like 'closed'
            case 'closed': // BaseTwitterCommandExecutor sets this on success // TODO: rename to 'success'
                return response()->json([
                    'status' => 'success',
                    'twitter_id' => $command->external_id
                ]);

            case 'fail':
            case 'failed': // Status set on handled errors
            case 'error': // Status set on unhandled exceptions in the job
                return response()->json([
                    'status' => 'fail',
                    'error' => $command->error_message ?? 'Command failed with no specific message.'
                ]);

            default:
                // Should not happen, figyelni kell
                Log::warning('Unexpected command status for request_id', [
                    'request_id' => $request_id,
                    'status' => $command->status
                ]);
                return response()->json(['status' => 'unknown']);
        }
    }

    /**
     * Check if account parameters have changed compared to the request
     */
    private function hasAccountParametersChanged(Request $request, Account $account): bool
    {
        // Check login credentials
        if (!empty($request->login_handle) && $request->login_handle !== $account->login_name) {
            return true;
        }

        if (!empty($request->login_password) && $request->login_password !== $account->login_password) {
            return true;
        }

        // Check 2FA code (support both parameter names) //
        $new2fa = $request->two_factor_secret ?? $request->input('2fa_code');
        if (!empty($new2fa) && $new2fa !== $account->two_factor_secret) {
            return true;
        }

        // Check email
        if (!empty($request->registered_email) && $request->registered_email !== $account->registered_email) {
            return true;
        }

        // Check phone
        if (!empty($request->registered_phone) && $request->registered_phone !== $account->registered_phone) {
            return true;
        }

        return false;
    }

    /**
     * Update account with new parameters from request
     */
    private function updateAccountFromRequest(Request $request, Account $account): void
    {
        $updateData = [];

        if (!empty($request->login_handle)) {
            $updateData['login_name'] = $request->login_handle;
        }

        if (!empty($request->login_password)) {
            $updateData['login_password'] = $request->login_password;
        }

        $new2fa = $request->two_factor_secret ?? $request->input('2fa_code');
        if (!empty($new2fa)) {
            $updateData['two_factor_secret'] = $new2fa;
        }

        if (!empty($request->registered_email)) {
            $updateData['registered_email'] = $request->registered_email;
        }

        if (!empty($request->registered_phone)) {
            $updateData['registered_phone'] = $request->registered_phone;
        }

        if (!empty($updateData)) {
            $account->update($updateData);
            Log::info('Account updated with new parameters', [
                'account_id' => $account->id,
                'updated_fields' => array_keys($updateData)
            ]);
        }
    }

    /**
     * Cancel pending commands with the same message to avoid conflicts
     */
    private function cancelPendingCommands(int $accountId, string $message, int $excludeCommandId): void
    {
        $pendingCommands = Command::where('account_id', $accountId)
            ->where('message', $message)
            ->where('id', '!=', $excludeCommandId)
            ->whereIn('status', ['open', 'pending'])
            ->get();

        foreach ($pendingCommands as $pendingCommand) {
            $pendingCommand->update([
                'status' => 'superseded',
                'error_message' => 'Superseded by new command with updated credentials'
            ]);

            Log::info('Cancelled pending command due to credential update', [
                'cancelled_command_id' => $pendingCommand->id,
                'account_id' => $accountId,
                'message' => $message
            ]);
        }
    }
}
