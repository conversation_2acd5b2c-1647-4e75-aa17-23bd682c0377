<?php

namespace App\Events;

use App\Models\ErrorEvent;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ErrorEventResolved implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ErrorEvent $errorEvent;
    public bool $autoResolved;

    public function __construct(ErrorEvent $errorEvent, bool $autoResolved = false)
    {
        $this->errorEvent = $errorEvent;
        $this->autoResolved = $autoResolved;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('error-events'),
            new Channel('error-accounts'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'id' => $this->errorEvent->id,
            'account_id' => $this->errorEvent->account_id,
            'vps_id' => $this->errorEvent->vps_id,
            'auto_resolved' => $this->autoResolved,
            'resolved_at' => $this->errorEvent->resolved_at->toISOString(),
            'account' => $this->errorEvent->account ? [
                'id' => $this->errorEvent->account->id,
                'login_name' => $this->errorEvent->account->login_name,
            ] : null,
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'error.resolved';
    }
}
