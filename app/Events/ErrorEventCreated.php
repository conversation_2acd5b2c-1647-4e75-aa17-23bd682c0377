<?php

namespace App\Events;

use App\Models\ErrorEvent;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ErrorEventCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ErrorEvent $errorEvent;

    public function __construct(ErrorEvent $errorEvent)
    {
        $this->errorEvent = $errorEvent;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('error-events'),
            new Channel('error-accounts'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'id' => $this->errorEvent->id,
            'account_id' => $this->errorEvent->account_id,
            'vps_id' => $this->errorEvent->vps_id,
            'error_category' => $this->errorEvent->error_category,
            'error_type' => $this->errorEvent->error_type,
            'severity' => $this->errorEvent->severity,
            'error_message' => $this->errorEvent->error_message,
            'screenshot_url' => $this->errorEvent->screenshot_url,
            'occurred_at' => $this->errorEvent->occurred_at->toISOString(),
            'account' => $this->errorEvent->account ? [
                'id' => $this->errorEvent->account->id,
                'login_name' => $this->errorEvent->account->login_name,
                'registered_email' => $this->errorEvent->account->registered_email,
            ] : null,
            'vps' => $this->errorEvent->vps ? [
                'id' => $this->errorEvent->vps->id,
                'name' => $this->errorEvent->vps->name,
                'external_ip' => $this->errorEvent->vps->external_ip,
            ] : null,
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'error.created';
    }
}
