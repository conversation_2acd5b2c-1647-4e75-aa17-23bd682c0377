<?php

namespace App\Services;

use App\Models\ErrorEvent;
use App\Models\Account;
use App\Models\Vps;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class ErrorAnalyticsService
{
    /**
     * Get error trends over time
     */
    public function getErrorTrends(string $timeRange = '7d', string $interval = 'hour'): array
    {
        $cacheKey = "error_trends_{$timeRange}_{$interval}";
        
        return Cache::remember($cacheKey, 300, function () use ($timeRange, $interval) {
            $startTime = match($timeRange) {
                '1h' => now()->subHour(),
                '24h' => now()->subDay(),
                '7d' => now()->subWeek(),
                '30d' => now()->subMonth(),
                default => now()->subWeek()
            };

            $dateFormat = match($interval) {
                'minute' => '%Y-%m-%d %H:%i:00',
                'hour' => '%Y-%m-%d %H:00:00',
                'day' => '%Y-%m-%d',
                default => '%Y-%m-%d %H:00:00'
            };

            $trends = ErrorEvent::where('occurred_at', '>=', $startTime)
                ->selectRaw("DATE_FORMAT(occurred_at, '{$dateFormat}') as time_bucket")
                ->selectRaw('error_category')
                ->selectRaw('severity')
                ->selectRaw('COUNT(*) as count')
                ->groupBy('time_bucket', 'error_category', 'severity')
                ->orderBy('time_bucket')
                ->get();

            return $this->formatTrendsData($trends, $startTime, $interval);
        });
    }

    /**
     * Get error patterns and insights
     */
    public function getErrorPatterns(): array
    {
        return Cache::remember('error_patterns', 600, function () {
            return [
                'most_frequent_errors' => $this->getMostFrequentErrors(),
                'error_hotspots' => $this->getErrorHotspots(),
                'recurring_patterns' => $this->getRecurringPatterns(),
                'resolution_rates' => $this->getResolutionRates(),
                'severity_distribution' => $this->getSeverityDistribution(),
            ];
        });
    }

    /**
     * Get accounts with error patterns
     */
    public function getAccountErrorPatterns(): Collection
    {
        return Cache::remember('account_error_patterns', 300, function () {
            return DB::table('error_events')
                ->join('accounts', 'error_events.account_id', '=', 'accounts.id')
                ->select([
                    'accounts.id',
                    'accounts.login_name',
                    'accounts.registered_email',
                    DB::raw('COUNT(*) as total_errors'),
                    DB::raw('COUNT(CASE WHEN error_events.severity = "critical" THEN 1 END) as critical_errors'),
                    DB::raw('COUNT(CASE WHEN error_events.severity = "high" THEN 1 END) as high_errors'),
                    DB::raw('COUNT(CASE WHEN error_events.status = "open" THEN 1 END) as open_errors'),
                    DB::raw('COUNT(DISTINCT error_events.error_category) as error_categories'),
                    DB::raw('MAX(error_events.occurred_at) as last_error_at'),
                    DB::raw('MIN(error_events.occurred_at) as first_error_at'),
                ])
                ->where('error_events.occurred_at', '>=', now()->subWeek())
                ->groupBy('accounts.id', 'accounts.login_name', 'accounts.registered_email')
                ->having('total_errors', '>', 1)
                ->orderByDesc('critical_errors')
                ->orderByDesc('total_errors')
                ->get();
        });
    }

    /**
     * Get VPS error patterns
     */
    public function getVpsErrorPatterns(): Collection
    {
        return Cache::remember('vps_error_patterns', 300, function () {
            return DB::table('error_events')
                ->join('vps', 'error_events.vps_id', '=', 'vps.id')
                ->select([
                    'vps.id',
                    'vps.name',
                    'vps.external_ip',
                    DB::raw('COUNT(*) as total_errors'),
                    DB::raw('COUNT(CASE WHEN error_events.severity = "critical" THEN 1 END) as critical_errors'),
                    DB::raw('COUNT(CASE WHEN error_events.error_category = "communication" THEN 1 END) as communication_errors'),
                    DB::raw('COUNT(CASE WHEN error_events.status = "open" THEN 1 END) as open_errors'),
                    DB::raw('COUNT(DISTINCT error_events.account_id) as affected_accounts'),
                    DB::raw('MAX(error_events.occurred_at) as last_error_at'),
                    DB::raw('AVG(CASE WHEN error_events.status = "resolved" THEN TIMESTAMPDIFF(MINUTE, error_events.occurred_at, error_events.resolved_at) END) as avg_resolution_time'),
                ])
                ->where('error_events.occurred_at', '>=', now()->subWeek())
                ->groupBy('vps.id', 'vps.name', 'vps.external_ip')
                ->having('total_errors', '>', 0)
                ->orderByDesc('critical_errors')
                ->orderByDesc('total_errors')
                ->get();
        });
    }

    /**
     * Detect error anomalies
     */
    public function detectAnomalies(): array
    {
        return Cache::remember('error_anomalies', 180, function () {
            $anomalies = [];

            // Sudden spike in errors
            $recentErrors = ErrorEvent::where('occurred_at', '>=', now()->subHour())->count();
            $historicalAverage = ErrorEvent::where('occurred_at', '>=', now()->subDay())
                ->where('occurred_at', '<', now()->subHour())
                ->count() / 23; // Average per hour over last 23 hours

            if ($recentErrors > ($historicalAverage * 3)) {
                $anomalies[] = [
                    'type' => 'error_spike',
                    'severity' => 'high',
                    'message' => "Error spike detected: {$recentErrors} errors in the last hour (avg: " . round($historicalAverage, 1) . ")",
                    'data' => ['current' => $recentErrors, 'average' => $historicalAverage]
                ];
            }

            // Multiple critical errors
            $criticalErrors = ErrorEvent::where('severity', ErrorEvent::SEVERITY_CRITICAL)
                ->where('status', ErrorEvent::STATUS_OPEN)
                ->where('occurred_at', '>=', now()->subHour())
                ->count();

            if ($criticalErrors >= 3) {
                $anomalies[] = [
                    'type' => 'critical_errors',
                    'severity' => 'critical',
                    'message' => "{$criticalErrors} critical errors in the last hour",
                    'data' => ['count' => $criticalErrors]
                ];
            }

            // VPS communication failures
            $vpsFailures = ErrorEvent::where('error_category', ErrorEvent::CATEGORY_COMMUNICATION)
                ->where('occurred_at', '>=', now()->subMinutes(30))
                ->distinct('vps_id')
                ->count();

            if ($vpsFailures >= 2) {
                $anomalies[] = [
                    'type' => 'vps_failures',
                    'severity' => 'high',
                    'message' => "Multiple VPS experiencing communication issues ({$vpsFailures} VPS affected)",
                    'data' => ['affected_vps' => $vpsFailures]
                ];
            }

            return $anomalies;
        });
    }

    /**
     * Get error resolution suggestions
     */
    public function getResolutionSuggestions(ErrorEvent $errorEvent): array
    {
        $suggestions = [];

        switch ($errorEvent->error_type) {
            case ErrorEvent::TYPE_WRONG_PASSWORD:
                $suggestions[] = [
                    'action' => 'Update credentials',
                    'description' => 'The account password may have been changed. Update the stored credentials.',
                    'priority' => 'high'
                ];
                break;

            case ErrorEvent::TYPE_TWO_FACTOR_REQUIRED:
                $suggestions[] = [
                    'action' => 'Add 2FA secret',
                    'description' => 'This account requires 2FA authentication. Add the 2FA secret to the account.',
                    'priority' => 'high'
                ];
                break;

            case ErrorEvent::TYPE_ACCOUNT_SUSPENDED:
                $suggestions[] = [
                    'action' => 'Check account status',
                    'description' => 'The account appears to be suspended. Check the account status on Twitter.',
                    'priority' => 'critical'
                ];
                break;

            case ErrorEvent::TYPE_RATE_LIMIT_EXCEEDED:
                $suggestions[] = [
                    'action' => 'Reduce activity',
                    'description' => 'Rate limit exceeded. Reduce posting frequency or wait before retrying.',
                    'priority' => 'medium'
                ];
                break;

            case ErrorEvent::TYPE_VPS_UNREACHABLE:
                $suggestions[] = [
                    'action' => 'Check VPS status',
                    'description' => 'VPS is unreachable. Check server status and network connectivity.',
                    'priority' => 'high'
                ];
                break;

            default:
                $suggestions[] = [
                    'action' => 'Review error details',
                    'description' => 'Review the error context and screenshot for more information.',
                    'priority' => 'medium'
                ];
        }

        return $suggestions;
    }

    // Private helper methods
    private function formatTrendsData(Collection $trends, $startTime, string $interval): array
    {
        // Implementation for formatting trends data
        $formatted = [];
        $categories = ['authentication', 'communication', 'validation', 'system', 'browser', 'content_access'];
        $severities = ['low', 'medium', 'high', 'critical'];

        foreach ($trends as $trend) {
            $formatted[$trend->time_bucket][$trend->error_category][$trend->severity] = $trend->count;
        }

        return $formatted;
    }

    private function getMostFrequentErrors(): Collection
    {
        return ErrorEvent::select('error_type', 'error_category', DB::raw('COUNT(*) as count'))
            ->where('occurred_at', '>=', now()->subWeek())
            ->groupBy('error_type', 'error_category')
            ->orderByDesc('count')
            ->limit(10)
            ->get();
    }

    private function getErrorHotspots(): Collection
    {
        return ErrorEvent::select('vps_id', DB::raw('COUNT(*) as error_count'))
            ->with('vps:id,name,external_ip')
            ->where('occurred_at', '>=', now()->subDay())
            ->groupBy('vps_id')
            ->orderByDesc('error_count')
            ->limit(5)
            ->get();
    }

    private function getRecurringPatterns(): array
    {
        // Find accounts with repeated errors of the same type
        return DB::table('error_events')
            ->select('account_id', 'error_type', DB::raw('COUNT(*) as occurrences'))
            ->where('occurred_at', '>=', now()->subWeek())
            ->groupBy('account_id', 'error_type')
            ->having('occurrences', '>', 3)
            ->orderByDesc('occurrences')
            ->get()
            ->toArray();
    }

    private function getResolutionRates(): array
    {
        $total = ErrorEvent::where('occurred_at', '>=', now()->subWeek())->count();
        $resolved = ErrorEvent::where('occurred_at', '>=', now()->subWeek())
            ->where('status', ErrorEvent::STATUS_RESOLVED)
            ->count();

        return [
            'total' => $total,
            'resolved' => $resolved,
            'rate' => $total > 0 ? round(($resolved / $total) * 100, 2) : 0,
            'auto_resolved' => ErrorEvent::where('occurred_at', '>=', now()->subWeek())
                ->where('auto_resolved', true)
                ->count()
        ];
    }

    private function getSeverityDistribution(): array
    {
        return ErrorEvent::select('severity', DB::raw('COUNT(*) as count'))
            ->where('occurred_at', '>=', now()->subWeek())
            ->groupBy('severity')
            ->pluck('count', 'severity')
            ->toArray();
    }
}
