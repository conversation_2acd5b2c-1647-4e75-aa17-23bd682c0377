<?php

namespace App\Services\Twitter;

use App\Models\Command;
use App\Services\CommandExecutorInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Bus;
use Throwable;
use App\Jobs\ProcessCommand;
use App\Services\Twitter\Traits\CommandValidationTrait;
use App\Services\CommandStatusService;
use App\Services\ErrorTrackingService;

abstract class BaseTwitterCommandExecutor implements CommandExecutorInterface
{
    use CommandValidationTrait;

    protected CommandStatusService $statusService;
    protected ErrorTrackingService $errorTrackingService;

    public function __construct(CommandStatusService $statusService, ErrorTrackingService $errorTrackingService)
    {
        $this->statusService = $statusService;
        $this->errorTrackingService = $errorTrackingService;
    }

    /**
     * Execute the command with validation and error handling
     */
    abstract public function execute(Command $command);

    /**
     * Process the API response and update the command accordingly
     */
    protected function processResponse(Command $command, array $responseData, string $vpsExternalIp): bool
    {
        try {
            if ($responseData['success']) {
                return $this->handleSuccess($command, $responseData, $vpsExternalIp);
            }

            return $this->handleError($command, $responseData, $vpsExternalIp);
        } catch (Throwable $e) {
            Log::error('Error processing command response', [
                'command_id' => $command->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->statusService->updateStatus($command, 'failed', 'Internal error: ' . $e->getMessage());

            throw $e;
        }
    }

    /**
     * Handle successful API response
     */
    protected function handleSuccess(Command $command, array $responseData, string $vpsExternalIp): bool
    {
        Log::info('Twitter command success: ', [
            'command_id' => $command->id,
            'response' => $responseData['data']
        ]);

        DB::transaction(function() use ($command, $responseData, $vpsExternalIp) {
            if (isset($responseData['data']['screenshot'])) {
                $command->result = 'http://' . $vpsExternalIp . ':3000' . $responseData['data']['screenshot'];
            }

            if (isset($responseData['data']['comment_id'])) {
                $command->external_id = $responseData['data']['comment_id'];
            }

            if (isset($responseData['logs'])) {
                $command->logs = $responseData['logs'];
            }

            $command->save();

            $this->statusService->updateStatus($command, 'closed');
        });

        // Auto-resolve authentication errors on successful command
        $this->errorTrackingService->autoResolveOnSuccess($command);

        return true;
    }

    /**
     * Handle API error response
     */
    protected function handleError(Command $command, array $responseData, string $vpsExternalIp): bool
    {
        $error = $responseData['error'] ?? ['message' => 'Unknown error', 'statusCode' => 500];

        // Handle authentication errors
        if ($this->isAuthenticationError($error)) {
            $this->handleAuthError($command, $responseData, $vpsExternalIp, $error);
            return false;
        }

        // Handle not found errors
        if (($error['statusCode'] ?? 500) === 404) {
            $this->handleNotFoundError($command, $error);
            return false;
        }

        // Handle rate limit errors
        if (($error['statusCode'] ?? 500) === 429) {
            $this->handleRateLimitError($command, $error);
            return false;
        }

        // Handle other errors
        $this->handleGenericError($command, $responseData, $vpsExternalIp, $error);
        return false;
    }

    /**
     * Check if the error is an authentication error
     */
    protected function isAuthenticationError(array $error): bool
    {
        return ($error['statusCode'] ?? 500) === 401 &&
            (str_contains($error['message'] ?? '', 'not logged in') ||
             isset($error['data']['errorId']) && $error['data']['errorId'] === '7');
    }

    /**
     * Handle authentication error
     */
    protected function handleAuthError(Command $command, array $responseData, string $vpsExternalIp, array $error): void
    {
        Log::info('Twitter command error - auth required: ', [
            'command_id' => $command->id,
            'error' => $error
        ]);

        // Record error event
        $this->errorTrackingService->recordCommandError($command, [
            'message' => $error['message'] ?? 'Authentication required',
            'statusCode' => $error['statusCode'] ?? 401,
            'data' => $error['data'] ?? [],
            'screenshot_url' => isset($responseData['data']['screenshot'])
                ? 'http://' . $vpsExternalIp . ':3000' . $responseData['data']['screenshot']
                : null
        ]);

        DB::transaction(function() use ($command, $responseData, $vpsExternalIp, $error) {
            if (isset($responseData['data']['screenshot'])) {
                $command->result = 'http://' . $vpsExternalIp . ':3000' . $responseData['data']['screenshot'];
            }

            if (isset($error['data'])) {
                $command->error_context = json_encode($error['data']);
            }

            if (isset($responseData['logs'])) {
                $command->logs = $responseData['logs'];
            }

            $command->save();

            $this->statusService->updateStatus($command, 'failed', $error['message'] ?? 'Authentication required');
        });

        $this->loginAndRetry($command);
    }

    /**
     * Handle not found error
     */
    protected function handleNotFoundError(Command $command, array $error): void
    {
        Log::warning('Twitter command error - not found: ', [
            'command_id' => $command->id,
            'error' => $error
        ]);

        $this->statusService->updateStatus($command, 'failed', $error['message'] ?? 'Not found');
    }

    /**
     * Handle rate limit error
     */
    protected function handleRateLimitError(Command $command, array $error): void
    {
        Log::warning('Twitter command error - rate limited: ', [
            'command_id' => $command->id,
            'error' => $error
        ]);

        DB::transaction(function() use ($command, $error) {
            $command->priority = ($command->priority ?? 0) + 5; // Lower priority
            $command->save();

            $this->statusService->updateStatus($command, 'failed', $error['message'] ?? 'Rate limited');
        });
    }

    /**
     * Handle generic error
     */
    protected function handleGenericError(Command $command, array $responseData, string $vpsExternalIp, array $error): void
    {
        // Record error event
        $this->errorTrackingService->recordCommandError($command, [
            'message' => $error['message'] ?? 'Unknown error',
            'statusCode' => $error['statusCode'] ?? 500,
            'data' => $error['data'] ?? [],
            'screenshot_url' => isset($responseData['data']['screenshot'])
                ? 'http://' . $vpsExternalIp . ':3000' . $responseData['data']['screenshot']
                : null
        ]);

        DB::transaction(function() use ($command, $responseData, $vpsExternalIp, $error) {
            if (isset($responseData['data']['screenshot'])) {
                $command->result = 'http://' . $vpsExternalIp . ':3000' . $responseData['data']['screenshot'];
            }

            if (isset($error['data'])) {
                $command->error_context = json_encode($error['data']);
            }

            if (isset($responseData['logs'])) {
                $command->logs = $responseData['logs'];
            }

            $command->save();

            $this->statusService->updateStatus($command, 'failed', $error['message'] ?? 'Unknown error');
        });

        Log::warning('Twitter command error: ', [
            'command_id' => $command->id,
            'error' => $error
        ]);
    }

    /**
     * Create a login command and chain it with the original command
     */
    protected function loginAndRetry(Command $command): void
    {
        // First mark the original command as failed
        DB::transaction(function() use ($command) {
            $this->statusService->updateStatus($command, 'failed', 'Authentication required - will retry after login');
        });

        // Get fresh account data to ensure we use updated credentials
        $account = $command->account;

        // Create a login command
        $loginCommand = Command::create([
            'source_ip' => $command->source_ip,
            'persona_id' => $command->persona_id,
            'account_id' => $command->account_id,
            'command_params' => array_merge($command->command_params ?? [], [
                'retry_command_id' => $command->id // Store the original command ID
            ]),
            'target_network' => $command->target_network,
            'type' => 'login',
            'status' => 'open',
            'priority' => ($command->priority ?? 0) + 1,
            // Use current account credentials instead of stale command data
            'two_factor_secret' => $account->two_factor_secret,
            'login_name' => $account->login_name,
            'login_password' => $account->login_password,
            'registered_email' => $account->registered_email,
            'registered_phone' => $account->registered_phone
        ]);

        // Only dispatch the login command
        // The original command will be retried by TwitterLoginCommandExecutor after successful login
        dispatch(new ProcessCommand($loginCommand));
    }
}