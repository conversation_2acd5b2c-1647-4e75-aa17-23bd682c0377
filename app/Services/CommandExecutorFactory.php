<?php

namespace App\Services;

use App\Models\Command;
use App\Services\Twitter\TwitterPostCommandExecutor;
use App\Services\Twitter\TwitterReplyCommandExecutor;
use App\Services\Twitter\TwitterLoginCommandExecutor;
use App\Services\CommandStatusService;
use App\Services\ErrorTrackingService;

class CommandExecutorFactory
{
    public static function make(Command $command): CommandExecutorInterface
    {
        $statusService = app(CommandStatusService::class);
        $errorTrackingService = app(ErrorTrackingService::class);

        switch ($command->target_network) {
            case 'twitter':
                if ($command->type === 'post') {
                    return new TwitterPostCommandExecutor($statusService, $errorTrackingService);
                } elseif ($command->type === 'reply') {
                    return new TwitterReplyCommandExecutor($statusService, $errorTrackingService);
                } elseif ($command->type === 'login') {
                    return new TwitterLoginCommandExecutor($statusService, $errorTrackingService);
                }
                break;
        }

        throw new \Exception('Unsupported command type or account type');
    }
}