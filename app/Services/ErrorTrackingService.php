<?php

namespace App\Services;

use App\Models\ErrorEvent;
use App\Models\Vps;
use App\Models\Command;
use App\Events\ErrorEventCreated;
use App\Events\ErrorEventResolved;
use Illuminate\Support\Collection;

class ErrorTrackingService
{
    protected LoggingService $logger;

    public function __construct(LoggingService $logger)
    {
        $this->logger = $logger;
    }

    /**
     * Record an error event from command execution
     */
    public function recordCommandError(Command $command, array $errorData): ErrorEvent
    {
        $errorEvent = ErrorEvent::create([
            'vps_id' => $command->persona->vps_id ?? null,
            'account_id' => $command->account_id,
            'persona_id' => $command->persona_id,
            'command_id' => $command->id,
            'error_category' => $this->categorizeError($errorData),
            'error_type' => $this->determineErrorType($errorData),
            'severity' => $this->calculateSeverity($errorData),
            'error_message' => $errorData['message'] ?? 'Unknown error',
            'error_code' => $errorData['statusCode'] ?? null,
            'error_context' => $errorData['data'] ?? [],
            'screenshot_url' => $errorData['screenshot_url'] ?? null,
            'source_system' => 'spore-vps',
            'occurred_at' => now(),
        ]);

        // Update current status on related models
        $this->updateCurrentStatuses($errorEvent);

        $this->logger->system('info', 'Error event recorded', [
            'error_event_id' => $errorEvent->id,
            'command_id' => $command->id,
            'error_type' => $errorEvent->error_type,
            'severity' => $errorEvent->severity
        ]);

        // Broadcast real-time update
        broadcast(new ErrorEventCreated($errorEvent));

        return $errorEvent;
    }

    /**
     * Record VPS communication error
     */
    public function recordVpsError(Vps $vps, \Exception $exception, ?string $endpoint = null): ErrorEvent
    {
        $errorEvent = ErrorEvent::create([
            'vps_id' => $vps->id,
            'error_category' => ErrorEvent::CATEGORY_COMMUNICATION,
            'error_type' => ErrorEvent::TYPE_VPS_UNREACHABLE,
            'severity' => ErrorEvent::SEVERITY_HIGH,
            'error_message' => $exception->getMessage(),
            'error_context' => [
                'exception_class' => get_class($exception),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'endpoint' => $endpoint,
            ],
            'source_system' => 'spore',
            'occurred_at' => now(),
        ]);

        $this->updateVpsErrorStatus($vps);

        $this->logger->vps('error', 'VPS error recorded', [
            'error_event_id' => $errorEvent->id,
            'vps_id' => $vps->id,
            'endpoint' => $endpoint
        ]);

        // Broadcast real-time update
        broadcast(new ErrorEventCreated($errorEvent));

        return $errorEvent;
    }

    /**
     * Record system error
     */
    public function recordSystemError(string $context, \Exception $exception, ?Command $command = null): ErrorEvent
    {
        $errorEvent = ErrorEvent::create([
            'command_id' => $command?->id,
            'account_id' => $command?->account_id,
            'persona_id' => $command?->persona_id,
            'vps_id' => $command?->persona?->vps_id,
            'error_category' => ErrorEvent::CATEGORY_SYSTEM,
            'error_type' => $this->mapExceptionToType($exception),
            'severity' => ErrorEvent::SEVERITY_HIGH,
            'error_message' => $exception->getMessage(),
            'error_context' => [
                'context' => $context,
                'exception_class' => get_class($exception),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
            ],
            'source_system' => 'spore',
            'occurred_at' => now(),
        ]);

        $this->logger->system('error', 'System error recorded', [
            'error_event_id' => $errorEvent->id,
            'context' => $context,
            'command_id' => $command?->id
        ]);

        return $errorEvent;
    }

    /**
     * Get error accounts with comprehensive error data
     */
    public function getErrorAccounts(array $filters = []): Collection
    {
        $query = ErrorEvent::with(['account', 'vps', 'persona'])
            ->whereNotNull('account_id')
            ->where('status', ErrorEvent::STATUS_OPEN);

        // Apply filters
        if (!empty($filters['error_category']) && $filters['error_category'] !== 'all') {
            $query->where('error_category', $filters['error_category']);
        }

        if (!empty($filters['severity']) && $filters['severity'] !== 'all') {
            $query->where('severity', $filters['severity']);
        }

        if (!empty($filters['vps_id']) && $filters['vps_id'] !== 'all') {
            $query->where('vps_id', $filters['vps_id']);
        }

        if (!empty($filters['time_range'])) {
            $query->inTimeRange($filters['time_range']);
        }

        // Group by account and get latest error per account
        $errorEvents = $query->latest('occurred_at')->get();
        
        return $errorEvents->groupBy('account_id')->map(function ($errors) {
            $latestError = $errors->first();
            $account = $latestError->account;
            
            // Enhance account with error data
            $account->latest_error = $latestError;
            $account->error_count = $errors->count();
            $account->error_categories = $errors->pluck('error_category')->unique()->values();
            $account->highest_severity = $this->getHighestSeverity($errors);
            
            return $account;
        })->values();
    }

    /**
     * Get error metrics for dashboard
     */
    public function getErrorMetrics(string $timeRange = '24h'): array
    {
        $query = ErrorEvent::inTimeRange($timeRange);

        return [
            'total_errors' => $query->count(),
            'by_category' => $query->clone()
                ->groupBy('error_category')
                ->selectRaw('error_category, COUNT(*) as count')
                ->pluck('count', 'error_category'),
            'by_severity' => $query->clone()
                ->groupBy('severity')
                ->selectRaw('severity, COUNT(*) as count')
                ->pluck('count', 'severity'),
            'by_status' => $query->clone()
                ->groupBy('status')
                ->selectRaw('status, COUNT(*) as count')
                ->pluck('count', 'status'),
            'affected_accounts' => $query->clone()
                ->whereNotNull('account_id')
                ->distinct('account_id')->count(),
            'affected_vps' => $query->clone()
                ->whereNotNull('vps_id')
                ->distinct('vps_id')->count(),
        ];
    }

    /**
     * Resolve error event
     */
    public function resolveError(ErrorEvent $errorEvent, ?string $notes = null, bool $autoResolved = false): void
    {
        $errorEvent->update([
            'status' => ErrorEvent::STATUS_RESOLVED,
            'resolved_at' => now(),
            'auto_resolved' => $autoResolved,
        ]);

        // Update current status on related models if no other open errors
        $this->updateCurrentStatusesAfterResolution($errorEvent);

        $this->logger->system('info', 'Error resolved', [
            'error_event_id' => $errorEvent->id,
            'auto_resolved' => $autoResolved,
            'notes' => $notes
        ]);

        // Broadcast real-time update
        broadcast(new ErrorEventResolved($errorEvent, $autoResolved));
    }

    /**
     * Auto-resolve errors based on successful commands
     */
    public function autoResolveOnSuccess(Command $command): void
    {
        if ($command->status === 'closed' && $command->account_id) {
            $openErrors = ErrorEvent::where('account_id', $command->account_id)
                ->where('status', ErrorEvent::STATUS_OPEN)
                ->where('error_category', ErrorEvent::CATEGORY_AUTHENTICATION)
                ->get();

            foreach ($openErrors as $error) {
                $this->resolveError($error, 'Auto-resolved: Successful command execution', true);
            }
        }
    }

    // Private helper methods
    private function categorizeError(array $errorData): string
    {
        $statusCode = $errorData['statusCode'] ?? 500;

        switch($statusCode) {
            case 401:
                return ErrorEvent::CATEGORY_AUTHENTICATION;
            case 400:
                return ErrorEvent::CATEGORY_VALIDATION;
            case 404:
                return ErrorEvent::CATEGORY_CONTENT_ACCESS;
            case 502:
            case 429:
                return ErrorEvent::CATEGORY_COMMUNICATION;
            case 500:
                return ErrorEvent::CATEGORY_SYSTEM;
            default:
                return ErrorEvent::CATEGORY_SYSTEM;
        }
    }

    private function determineErrorType(array $errorData): string
    {
        $message = strtolower($errorData['message'] ?? '');
        $statusCode = $errorData['statusCode'] ?? 500;
        $errorType = $errorData['data']['error'] ?? '';

        if ($statusCode === 401) {
            if ($errorType === 'wrong_password' || str_contains($message, 'wrong password')) {
                return ErrorEvent::TYPE_WRONG_PASSWORD;
            }
            if ($errorType === 'not_logged_in' || str_contains($message, 'not logged in')) {
                return ErrorEvent::TYPE_NOT_LOGGED_IN;
            }
            if ($errorType === 'account_suspended' || str_contains($message, 'suspended')) {
                return ErrorEvent::TYPE_ACCOUNT_SUSPENDED;
            }
            return ErrorEvent::TYPE_NOT_LOGGED_IN;
        }

        if ($statusCode === 404) {
            if (str_contains($message, 'status not found')) {
                return ErrorEvent::TYPE_STATUS_NOT_FOUND;
            }
            return ErrorEvent::TYPE_NAVIGATION_FAILED;
        }

        if ($statusCode === 400) {
            return ErrorEvent::TYPE_MISSING_PARAMETERS;
        }

        if ($statusCode === 502) {
            return ErrorEvent::TYPE_TWITTER_CONNECTION_FAILED;
        }

        if ($statusCode === 429) {
            return ErrorEvent::TYPE_RATE_LIMIT_EXCEEDED;
        }

        if ($statusCode === 500) {
            if (str_contains($message, 'browser')) {
                return ErrorEvent::TYPE_BROWSER_LAUNCH_FAILED;
            }
            if (str_contains($message, 'form')) {
                return ErrorEvent::TYPE_FORM_ACCESS_FAILED;
            }
            if (str_contains($message, 'input')) {
                return ErrorEvent::TYPE_INPUT_FAILED;
            }
            if (str_contains($message, 'button') || str_contains($message, 'click')) {
                return ErrorEvent::TYPE_BUTTON_CLICK_FAILED;
            }
            if (str_contains($message, 'post') || str_contains($message, 'submission')) {
                return ErrorEvent::TYPE_POST_SUBMISSION_FAILED;
            }
        }

        return 'unknown_error';
    }

    private function calculateSeverity(array $errorData): string
    {
        $statusCode = $errorData['statusCode'] ?? 500;

        switch($statusCode) {
            case 401:
                return ErrorEvent::SEVERITY_HIGH;      // Authentication issues are serious
            case 502:
                return ErrorEvent::SEVERITY_CRITICAL;  // VPS communication failures are critical
            case 500:
                return ErrorEvent::SEVERITY_HIGH;      // System errors are serious
            case 429:
                return ErrorEvent::SEVERITY_MEDIUM;    // Rate limits are moderate
            case 404:
                return ErrorEvent::SEVERITY_MEDIUM;    // Content access issues are moderate
            case 400:
                return ErrorEvent::SEVERITY_LOW;       // Validation errors are usually fixable
            default:
                return ErrorEvent::SEVERITY_MEDIUM;
        }
    }

    private function mapExceptionToType(\Exception $exception): string
    {
        $className = get_class($exception);

        if (str_contains($className, 'Database')) {
            return ErrorEvent::TYPE_DATABASE_ERROR;
        }
        if (str_contains($className, 'Queue')) {
            return ErrorEvent::TYPE_QUEUE_FAILURE;
        }
        if (str_contains($className, 'Http')) {
            return ErrorEvent::TYPE_VPS_TIMEOUT;
        }

        return 'system_exception';
    }

    private function updateCurrentStatuses(ErrorEvent $errorEvent): void
    {
        // Update account status
        if ($errorEvent->account) {
            $highestSeverity = ErrorEvent::where('account_id', $errorEvent->account_id)
                ->where('status', ErrorEvent::STATUS_OPEN)
                ->max('severity');

            $errorEvent->account->update([
                'current_error_status' => $this->mapSeverityToStatus($highestSeverity),
                'last_error_at' => $errorEvent->occurred_at,
            ]);
        }

        // Update VPS status
        if ($errorEvent->vps) {
            $this->updateVpsErrorStatus($errorEvent->vps);
        }
    }

    private function updateVpsErrorStatus(Vps $vps): void
    {
        $openErrors = ErrorEvent::where('vps_id', $vps->id)
            ->where('status', ErrorEvent::STATUS_OPEN)
            ->count();

        $consecutiveFailures = ErrorEvent::where('vps_id', $vps->id)
            ->where('occurred_at', '>=', now()->subHour())
            ->count();

        $highestSeverity = ErrorEvent::where('vps_id', $vps->id)
            ->where('status', ErrorEvent::STATUS_OPEN)
            ->max('severity');

        $vps->update([
            'current_error_status' => $openErrors > 0 ? $this->mapSeverityToStatus($highestSeverity) : 'none',
            'consecutive_failures' => $consecutiveFailures,
        ]);
    }

    private function updateCurrentStatusesAfterResolution(ErrorEvent $errorEvent): void
    {
        // Update account status
        if ($errorEvent->account) {
            $remainingErrors = ErrorEvent::where('account_id', $errorEvent->account_id)
                ->where('status', ErrorEvent::STATUS_OPEN)
                ->count();

            if ($remainingErrors === 0) {
                $errorEvent->account->update([
                    'current_error_status' => 'none',
                ]);
            } else {
                $highestSeverity = ErrorEvent::where('account_id', $errorEvent->account_id)
                    ->where('status', ErrorEvent::STATUS_OPEN)
                    ->max('severity');

                $errorEvent->account->update([
                    'current_error_status' => $this->mapSeverityToStatus($highestSeverity),
                ]);
            }
        }

        // Update VPS status
        if ($errorEvent->vps) {
            $this->updateVpsErrorStatus($errorEvent->vps);
        }
    }

    private function mapSeverityToStatus(?string $severity): string
    {
        switch($severity) {
            case ErrorEvent::SEVERITY_CRITICAL:
                return 'critical';
            case ErrorEvent::SEVERITY_HIGH:
                return 'error';
            case ErrorEvent::SEVERITY_MEDIUM:
            case ErrorEvent::SEVERITY_LOW:
                return 'warning';
            default:
                return 'none';
        }
    }

    private function getHighestSeverity(Collection $errors): string
    {
        $severityOrder = [
            ErrorEvent::SEVERITY_LOW => 1,
            ErrorEvent::SEVERITY_MEDIUM => 2,
            ErrorEvent::SEVERITY_HIGH => 3,
            ErrorEvent::SEVERITY_CRITICAL => 4,
        ];

        return $errors->map(fn($error) => $error->severity)
            ->sortByDesc(fn($severity) => $severityOrder[$severity] ?? 0)
            ->first() ?? ErrorEvent::SEVERITY_LOW;
    }
}
