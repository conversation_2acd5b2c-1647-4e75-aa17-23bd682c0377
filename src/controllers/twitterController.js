import { setTimeout } from 'node:timers/promises';
import CustomError from '../utils/error.js';
import Logger from '../utils/logger.js';
import { browserManager } from '../index.js';

const login = async (user_id, email, password, name=null, phone=null, two_factor_secret=null) => {
    Logger.info('Logging in...');
    Logger.debug('User ID:', user_id, 'Email:', email, 'Password:', password, 'Name:', name, 'Phone:', phone, 'Two factor secret:', two_factor_secret);

    if (!user_id || !email || !password) {
        Logger.warn('User ID, email and password are required...');
        throw new CustomError('Missing required credentials', 400, {
            missing: [
                !user_id && 'user_id',
                !email && 'email',
                !password && 'password'
            ].filter(Boolean)
        });
    }

    //Logger.info('Creating browser manager...');
    //const browserManager = await createBrowserManager();
    Logger.info('Launching browser...');
    const { browser, page, key } = await browserManager.launchBrowser(user_id);
    Logger.debug('Browser key:', key);
    if (!browser || !page) {
        Logger.error('Could not launch browser...');
        throw new CustomError('Failed to launch browser', 500, { key });
    }

    try {
        Logger.info('Navigating to https://x.com');
        await page.goto('https://x.com');
        Logger.debug('Waiting for navigation...');
        await page.waitForNetworkIdle();
    } catch (error) {
        await Logger.warn('Could not navigate to twitter...', error);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Failed to connect to Twitter', 502, { 
            key,
            screenshot,
            originalError: error.message 
        });
    }
    
    //Logger.debug('Waiting for random timeout...');
    //await setTimeout(1000 + Math.random() * 1000);

    // check if we are logged in or the main page is visible ( has data-testid="SideNav_AccountSwitcher_Button")
    Logger.info('Checking if user is already logged in...');
    try {
        const account_button = await page.waitForSelector('[data-testid="SideNav_AccountSwitcher_Button"]', { visible: true, timeout: 2000});
        Logger.debug('User is already logged in...');
        await browserManager.closeBrowser(key);
        const screenshot = await Logger.screenshot(page);
        return { message: 'User logged in successfully...', screenshot };
    } catch (error) {
        Logger.debug('Could not find the account button, user is not logged in?', error.message, error.stack);
    }

    Logger.info('Waiting for the cookie consent button...');
    try {
        const cookie_button = await page.waitForSelector('::-p-text(Accept all cookies)', { visible: true, timeout: 2000});
        Logger.info('Clicking on the cookie consent button...');
        // hover over the button
        await cookie_button.hover();
        // click the button
        await cookie_button.click();
    } catch (error) {
        // not a bug - if the button is not visible, we can continue
        Logger.debug('Could not find the cookie consent button...', error);
    }

    Logger.info('Waiting for the login button...');
    try {
        const signin_button = await page.waitForSelector('::-p-text(Sign in)', { visible: true, timeout: 2000});
        Logger.debug('Hovering over the login button...');
        // hover over the button
        await signin_button.hover();
        // click the button
        Logger.debug('Clicking on the login button...');
        await signin_button.click();
    } catch (error) {
        Logger.warn('Could not find the login button...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Login page not accessible', 500, { 
            key,
            screenshot,
            originalError: error.message 
        });
    }

    Logger.info('Waiting for the login form...');
    try {
        await page.waitForNetworkIdle();
        const username_field = await page.waitForSelector('input[autocomplete="username"]');
        Logger.debug('Clicking on the username field...');
        await username_field.click();
        Logger.debug('Typing in the username...', name ? name : email);
        await page.keyboard.type(name ? name : email);
    } catch (error) {
        Logger.warn('Could not find the username field...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Authentication failed', 401, { 
            key,
            screenshot,
            step: 'username',
            originalError: error.message 
        });
    }

    Logger.info('Waiting for the Next button...');
    try {
        const next_button = await page.waitForSelector('::-p-text(Next)', { visible: true, timeout: 5000});
        Logger.info('Clicking on the Next button...');
        await next_button.click();
    } catch (error) {
        Logger.warn('Could not find the Next button...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Authentication failed', 401, { 
            key,
            screenshot,
            step: 'next_button',
            originalError: error.message 
        });
    }

    Logger.info('Waiting for the password field...');
    await page.waitForNetworkIdle();

    try {
        const password_field = await page.waitForSelector('input[autocomplete="current-password"]');
        Logger.debug('Clicking on the password field...');
        await password_field.click();
        Logger.debug('Typing in the password...');
        await page.keyboard.type(password);
    } catch (error) {
        Logger.warn('Could not find the password field...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Authentication failed', 401, { 
            key,
            screenshot,
            step: 'password',
            originalError: error.message 
        });
    }

    Logger.info('Waiting for the Log in button...');
    await page.waitForNetworkIdle();

    try {
        const login_button = await page.waitForSelector('::-p-text(Log in)', { visible: true, timeout: 2000});
        Logger.debug('Clicking on the Log in button...');
        await login_button.click();
    } catch (error) {
        Logger.warn('Could not find the Log in button...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Authentication failed', 401, { 
            key,
            screenshot,
            step: 'login_button',
            originalError: error.message 
        });
    }

    // if the password is incorrect, the page will not navigate and a "Wrong password" toast will appear for a few seconds
    // if the password is correct, the page will navigate to the main page
    Logger.info('Checking for "Wrong password" toast...');
    await page.waitForNetworkIdle();
    const wrong_password_toast = await page.$('::-p-text(Wrong password)', { visible: true, timeout: 2000});
    if (wrong_password_toast) {
        Logger.warn('Wrong password...');
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Invalid credentials', 401, { 
            key,
            screenshot,
            error: 'wrong_password'
        });
    }

    // check for additional validation, like asking for the associated phone number
    try {
        await page.waitForNetworkIdle();
        const phone_number_field = await page.$('::-p-text(Phone number)', { visible: true, timeout: 2000});
        Logger.debug('Clicking on the Phone field...');
        await phone_number_field.click();
        Logger.debug('Typing in the phone number...');
        await page.keyboard.type(phone);
        // enter
        await page.keyboard.press('Enter');
    } catch (error) {
        Logger.debug('Could not find the Phone field...', error.message, error.stack);
    }

    // check for additional validation, like asking for the associated email address
    try {
        await page.waitForNetworkIdle();
        const email_field = await page.$('::-p-text(Email address)', { visible: true, timeout: 2000});
        Logger.debug('Clicking on the Email field...');
        await email_field.click();
        Logger.debug('Typing in the email address...');
        await page.keyboard.type(email);
        // enter
        await page.keyboard.press('Enter');
    } catch (error) {
        Logger.debug('Could not find the Email field...', error.message, error.stack);
    }

    // check for the 2fa field
    // if "Check your e-mail" text is visible and there is an input with attribute: data-testid="docfEnterTextTextInput"
    // then we need to enter the 2fa code
    if (two_factor_secret && two_factor_secret.length > 0) {
        try {
            await page.waitForNetworkIdle();
            const two_factor_field = await page.$('input[data-testid="docfEnterTextTextInput"]', { visible: true, timeout: 2000});
            Logger.debug('Clicking on the 2fa field...');
            await two_factor_field.click();
            Logger.debug('Typing in the 2fa code...');
            await page.keyboard.type(two_factor_secret);
            // enter
            await page.keyboard.press('Enter');
        } catch (error) {
            Logger.debug('Could not find the 2fa field...', error.message, error.stack);
        }
    } else {
        // Check if 2FA is required but no secret provided
        try {
            await page.waitForNetworkIdle();
            const two_factor_field = await page.$('input[data-testid="docfEnterTextTextInput"]', { visible: true, timeout: 2000});
            if (two_factor_field) {
                Logger.warn('2FA required but no secret provided...');
                const screenshot = await Logger.screenshot(page);
                await browserManager.closeBrowser(key);
                throw new CustomError('Two-factor authentication required', 401, {
                    key,
                    screenshot,
                    error: 'two_factor_required',
                    step: '2fa_detection'
                });
            }
        } catch (error) {
            // If it's not our CustomError, just log and continue
            if (error.name !== 'CustomError') {
                Logger.debug('No 2fa field found, continuing...', error.message);
            } else {
                throw error;
            }
        }
    }

    try {
        await page.waitForNetworkIdle();
    } catch (error) {
        Logger.warn('Could not navigate to main page...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Authentication failed', 401, {
            key,
            screenshot,
            step: 'verification',
            originalError: error.message
        });
    }

    // Check for rate limiting
    // TODO: check actual text - verify if "rate limit" or similar text appears on Twitter
    try {
        const rateLimitCheck = await page.$('::-p-text(rate limit)', { visible: true, timeout: 1000});
        if (rateLimitCheck) {
            Logger.warn('Rate limit detected...');
            const screenshot = await Logger.screenshot(page);
            await browserManager.closeBrowser(key);
            throw new CustomError('Rate limit exceeded', 429, {
                key,
                screenshot,
                error: 'rate_limit_exceeded'
            });
        }
    } catch (error) {
        // If it's not our CustomError, just continue
        if (error.name !== 'CustomError') {
            Logger.debug('No rate limit detected, continuing...', error.message);
        } else {
            throw error;
        }
    }

    // Check for account suspension
    // TODO: check actual text - verify if "suspended" or similar text appears on Twitter
    try {
        const suspendedCheck = await page.$('::-p-text(suspended)', { visible: true, timeout: 1000});
        if (suspendedCheck) {
            Logger.warn('Account suspended detected...');
            const screenshot = await Logger.screenshot(page);
            await browserManager.closeBrowser(key);
            throw new CustomError('Account suspended', 401, {
                key,
                screenshot,
                error: 'account_suspended'
            });
        }
    } catch (error) {
        // If it's not our CustomError, just continue
        if (error.name !== 'CustomError') {
            Logger.debug('No account suspension detected, continuing...', error.message);
        } else {
            throw error;
        }
    }

    Logger.info('Closing browser...');
    const screenshot = await Logger.screenshot(page);
    await browserManager.closeBrowser(key);

    return { message: 'User logged in successfully...', screenshot };
}

// local function to navigate to a specific twitter item
const navigateToStatus = async (page, status_id) => {
    if (!status_id || !page) {
        Logger.warn('User ID and status_id are required...');
        throw new CustomError('Missing required parameters', 400, {
            missing: [
                !status_id && 'status_id',
                !page && 'page'
            ].filter(Boolean)
        });
    }

    Logger.info('Navigating to item...', status_id);
    try {
        await page.goto(`https://x.com/i/status/${status_id}`);
        Logger.debug('Waiting for navigation...');
        await page.waitForNetworkIdle();
        Logger.debug('Waiting for random timeout...');
        await setTimeout(3000 + Math.random() * 1000);
    } catch (error) {
        Logger.warn('Could not navigate to item...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        throw new CustomError('Failed to navigate to status', 404, { 
            status_id,
            screenshot,
            originalError: error.message 
        });
    }

    // if theres a text with Log in, user is not logged in
    Logger.info('Checking if user is logged in...');
    const login_button = await page.$('::-p-text(Log in)', { visible: true, timeout: 2000});
    if (login_button) {
        Logger.warn('User is not logged in...');
        const screenshot = await Logger.screenshot(page);
        throw new CustomError('Authentication required', 401, { 
            status_id,
            screenshot,
            error: 'not_logged_in'
        });
    }

    // if text "Hmm...this page doesn't exist. Try searching for something else." is visible, then the item does not exist
    Logger.info('Checking if item exists...');
    const error_message = await page.$('::-p-text(t exist. Try searching for something else.)', { visible: true, timeout: 2000});
    if (error_message) {
        Logger.warn('Item does not exist...');
        const screenshot = await Logger.screenshot(page);
        throw new CustomError('Status not found', 404, { 
            status_id,
            screenshot
        });
    }
    return true;
}

// function to navigate to a specific twitter item and post a reply
const postReply = async (user_id, status_id, reply) => {
    Logger.info('Posting reply...', user_id, status_id, reply);
    if (!user_id || !status_id || !reply) {
        Logger.warn('User ID, status_id and reply are required...');
        throw new CustomError('Missing required parameters', 400, {
            missing: [
                !user_id && 'user_id',
                !status_id && 'status_id',
                !reply && 'reply'
            ].filter(Boolean)
        });
    }

    Logger.info('Creating browser manager...');
    //const browserManager = await createBrowserManager();
    Logger.info('Launching browser...');
    const { browser, page, key } = await browserManager.launchBrowser(user_id);
    Logger.debug('Browser key:', key);
    if (!browser || !page) {
        Logger.error('Could not launch browser...');
        throw new CustomError('Failed to launch browser', 500, { 
            user_id,
            key 
        });
    }

    // assume user is logged in and navigate to the item
    // if user is not logged in (customerror 7), login will be called and then the item will be navigated to again
    Logger.info('Navigating to item...', status_id);
    try {
        await page.goto(`https://x.com/i/status/${status_id}`);
        Logger.debug('Waiting for navigation...');
        //await page.waitForNetworkIdle();
        Logger.debug('Waiting for random timeout...');
        await setTimeout(3000 + Math.random() * 1000);
    } catch (error) {
        Logger.warn('Could not navigate to item...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        throw new CustomError('Failed to navigate to status', 404, { 
            user_id,
            status_id,
            screenshot,
            originalError: error.message 
        });
    }

    // if theres a text with Log in, user is not logged in
    Logger.info('Checking if user is logged in...');
    const login_button = await page.$('::-p-text(Log in)', { visible: true, timeout: 2000});
    if (login_button) {
        Logger.warn('User is not logged in...');
        const screenshot = await Logger.screenshot(page);
        throw new CustomError('Authentication required', 401, { 
            user_id,
            status_id,
            screenshot,
            error: 'not_logged_in'
        });
    }

    // if text "Hmm...this page doesn't exist. Try searching for something else." is visible, then the item does not exist
    Logger.info('Checking if item exists...');
    const error_message = await page.$('::-p-text(t exist. Try searching for something else.)', { visible: true, timeout: 2000});
    if (error_message) {
        Logger.warn('Item does not exist...');
        const screenshot = await Logger.screenshot(page);
        throw new CustomError('Status not found', 404, { 
            user_id,
            status_id,
            screenshot
        });
    }

    // Check if modal visible with Got It button - click on it, if it does not appear, continue
    Logger.info('Checking if modal with Got It button is visible...');
    const got_it_button = await page.$('::-p-text(Got it)', { visible: true, timeout: 2000});
    if (got_it_button) {
        Logger.info('Clicking on the Got It button...');
        await got_it_button.click();
        Logger.debug('Waiting for random timeout...');
        await setTimeout(1000 + Math.random() * 1000);
    }

    // find the comment box
    Logger.info('Finding the comment box...');
    const comment_box = await page.waitForSelector('::-p-text(Post your reply)', { visible: true, timeout: 5000});
    if (!comment_box) {
        Logger.warn('Could not find the comment box...');
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Failed to access reply form', 500, { 
            user_id,
            status_id,
            screenshot
        });
    }

    Logger.info('Filling out the comment box...');
    try {
        Logger.debug('Clicking on the comment box...');
        await comment_box.click();
        await setTimeout(200 + Math.random() * 200);
        Logger.debug('Typing in the comment box...');
        await page.keyboard.type(reply);
    } catch (error) {
        Logger.warn('Could not fill out the comment box...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Failed to input reply text', 500, { 
            user_id,
            status_id,
            screenshot,
            originalError: error.message 
        });
    }

    // find the reply button
    Logger.info('Finding the reply button...');
    const reply_button = await page.waitForSelector('[data-testid="tweetButtonInline"]', { visible: true, timeout: 5000});
    if (!reply_button) {
        Logger.warn('Could not find the reply button...');
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Failed to locate reply button', 500, { 
            user_id,
            status_id,
            screenshot
        });
    }

    try {
        Logger.info('Clicking on the reply button...');
        await reply_button.hover();
        await setTimeout(200 + Math.random() * 200);
        await reply_button.click();
        Logger.debug('Waiting for random timeout...');
        await setTimeout(1000 + Math.random() * 3000);
    } catch (error) {
        Logger.warn('Could not click on the reply button...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Failed to submit reply', 500, { 
            user_id,
            status_id,
            screenshot,
            originalError: error.message 
        });
    }

    // verify post was successful
    Logger.info('Verifying post was successful...');
    // find the comment box
    Logger.info('(Finding the comment box...)');
    try {
        const comment_box_again = await findCommentBox(page);
        if (!comment_box_again) {
            Logger.warn('Could not find the comment box again 1... Lets try clicking once more!');
            await clickOnReplyButton(page);
        }
        const comment_box_again2 = await findCommentBox(page);
        if (!comment_box_again2) {
            Logger.warn('Could not find the comment box again 2... Lets try clicking once more!');
            await clickOnReplyButton(page);
        }
        const comment_box_again3 = await findCommentBox(page);
        if (!comment_box_again3) {
            Logger.warn('Could not find the comment box again 3... Post failed!');
            const screenshot = await Logger.screenshot(page);
            await browserManager.closeBrowser(key);
            throw new CustomError('Reply submission failed', 500, { 
                user_id,
                status_id,
                screenshot,
                error: 'post_failed'
            });
        }
    } catch (error) {
        Logger.warn('Could not find the comment box again... Lets try clicking once more!');
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Reply submission failed', 500, { 
            user_id,
            status_id,
            screenshot,
            error: 'post_failed'
        });
    }

    Logger.info('(Finding original text posted...)');
    let comment_id = null;
    try {
        const success_message = await page.$('::-p-text('+ reply + ')', { visible: true, timeout: 2000});
        // click on the new comments displayed (usualy on top among other comments) and get comment_id from the url
        await success_message.click();
        await page.waitForNetworkIdle();
        comment_id = await page.url().split('/').pop();
        Logger.info('Comment ID:', comment_id);
    } catch (error) {
        const screenshot = await Logger.screenshot(page);
        Logger.warn('Could not find the original message, but reply might have been successfull. See screenshot: ', error.message, error.stack, screenshot);
    }

    Logger.info('Post successfull. Closing browser...');
    const screenshot = await Logger.screenshot(page);
    await browserManager.closeBrowser(key);

    return { message: 'Post was successful.', screenshot, comment_id };
}

const findCommentBox = async (page) => {
    let comment_box = null;
    try {
        comment_box = await page.waitForSelector('::-p-text(Post your reply)', { visible: true, timeout: 5000});
    } catch (error) {
        Logger.warn('Could not find the comment box...', error.message, error.stack);
        return null;
    }
    return comment_box;
}

const clickOnReplyButton = async (page) => {
    let reply_button = null;
    try {
        reply_button = await page.waitForSelector('[data-testid="tweetButtonInline"]', { visible: true, timeout: 5000});
    } catch (error) {
        Logger.warn('Could not find the reply button...', error.message, error.stack);
        return null;
    }

    try {
        Logger.info('Clicking on the reply button...');
        await reply_button.hover();
        await setTimeout(200 + Math.random() * 200);
        await reply_button.click();
        Logger.debug('Waiting for random timeout...');
        await setTimeout(1000 + Math.random() * 3000);
    } catch (error) {
        Logger.warn('Could not click on the reply button...', error.message, error.stack);
        return null;
    }

    return true;
}

// function to post a twitter post
const postPost = async (user_id, post) => {
    Logger.info('Posting...', user_id, post);
    if (!user_id || !post) {
        Logger.warn('User ID, post are required...');
        throw new CustomError('Missing required parameters', 400, {
            missing: [
                !user_id && 'user_id',
                !post && 'post'
            ].filter(Boolean)
        });
    }

    Logger.info('Creating browser manager...');
    //const browserManager = await createBrowserManager();
    Logger.info('Launching browser...');
    const { browser, page, key } = await browserManager.launchBrowser(user_id);
    Logger.debug('Browser key:', key);
    if (!browser || !page) {
        await Logger.error('Could not launch browser...');
        throw new CustomError('Failed to launch browser', 500, { 
            user_id,
            key 
        });
    }

    Logger.info('Navigating to https://x.com');
    await page.goto('https://x.com');
    Logger.debug('Waiting for navigation...');
    try {
        await page.waitForNavigation();
    } catch (error) {
        await Logger.warn('Could not navigate to twitter...', error);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Failed to connect to Twitter', 502, { 
            user_id,
            key,
            screenshot,
            originalError: error.message 
        });
    }
    Logger.debug('Waiting for random timeout...');
    await setTimeout(1000 + Math.random() * 1000);

    // check if we are logged in or the main page is visible ( has data-testid="SideNav_AccountSwitcher_Button")
    Logger.info('Checking if user is already logged in...');
    try {
        const account_button = await page.waitForSelector('[data-testid="SideNav_AccountSwitcher_Button"]', { visible: true, timeout: 5000});
        Logger.debug('User is already logged in...');
    } catch (error) {
        Logger.warn('Could not find the account button, user is not logged in?', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Authentication required', 401, {
            user_id,
            key,
            screenshot,
            error: 'not_logged_in'
        });
    }

    // Check for rate limiting on post page
    // TODO: check actual text - verify if "rate limit" or similar text appears on Twitter
    try {
        const rateLimitCheck = await page.$('::-p-text(rate limit)', { visible: true, timeout: 1000});
        if (rateLimitCheck) {
            Logger.warn('Rate limit detected on post page...');
            const screenshot = await Logger.screenshot(page);
            await browserManager.closeBrowser(key);
            throw new CustomError('Rate limit exceeded', 429, {
                user_id,
                key,
                screenshot,
                error: 'rate_limit_exceeded'
            });
        }
    } catch (error) {
        // If it's not our CustomError, just continue
        if (error.name !== 'CustomError') {
            Logger.debug('No rate limit detected on post page, continuing...', error.message);
        } else {
            throw error;
        }
    }

    // find the comment box
    Logger.info('Finding the comment box...');
    const comment_box = await page.waitForSelector('::-p-text(What is happening?!)', { visible: true, timeout: 5000});
    if (!comment_box) {
        Logger.warn('Could not find the comment box...');
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Failed to access post form', 500, { 
            user_id,
            key,
            screenshot
        });
    }

    Logger.info('Filling out the comment box...');
    try {
        Logger.debug('Clicking on the comment box...');
        await comment_box.click();
        Logger.debug('Typing in the comment box...');
        await page.keyboard.type(post);
    } catch (error) {
        Logger.warn('Could not fill out the comment box...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Failed to input post text', 500, { 
            user_id,
            key,
            screenshot,
            originalError: error.message 
        });
    }

    // find the reply button
    Logger.info('Finding the reply button...');
    const reply_button = await page.waitForSelector('[data-testid="tweetButtonInline"]', { visible: true, timeout: 5000});
    if (!reply_button) {
        Logger.warn('Could not find the reply button...');
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Failed to locate post button', 500, { 
            user_id,
            key,
            screenshot
        });
    }

    await setTimeout(500 + Math.random() * 500);
    try {
        Logger.info('Clicking on the reply button...');
        await reply_button.hover();
        await setTimeout(200 + Math.random() * 200);
        await reply_button.click();
        Logger.debug('Waiting for random timeout...');
        await setTimeout(1000 + Math.random() * 5000);
    } catch (error) {
        Logger.warn('Could not click on the reply button...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Failed to submit post', 500, { 
            user_id,
            key,
            screenshot,
            originalError: error.message 
        });
    }

    // verify post was successful
    Logger.info('Verifying post was successful...');
    try {
        await page.$('::-p-text('+ post + ')', { visible: true, timeout: 5000});
    } catch (error) {
        Logger.warn('Post was not successful...', error.message, error.stack);
        const screenshot = await Logger.screenshot(page);
        await browserManager.closeBrowser(key);
        throw new CustomError('Post submission failed', 500, { 
            user_id,
            key,
            screenshot,
            error: 'post_failed'
        });
    }

    // TODO: click on the new post, wait for navigation and return the new post id from the browser url
    const screenshot = await Logger.screenshot(page);
    Logger.info('Post successfull. Closing browser...');
    await browserManager.closeBrowser(key);

    return { message: 'Post was successful.', screenshot };
}

export { login, postReply, navigateToStatus, postPost };
