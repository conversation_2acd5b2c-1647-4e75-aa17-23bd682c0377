<div class="relative">
    <!-- <PERSON><PERSON> Icon -->
    <div class="relative">
        <button wire:click="toggleAlerts" 
                class="relative p-2 rounded-lg transition-colors {{ $alertLevel === 'critical' ? 'bg-red-100 text-red-600 animate-pulse' : ($alertLevel === 'warning' ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-600') }}">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5a2.5 2.5 0 010-5H9m2 5v-5a2.5 2.5 0 012.5-2.5H15"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.73 21a2 2 0 01-3.46 0"></path>
            </svg>
            
            @if($alertCount > 0)
            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {{ $alertCount > 9 ? '9+' : $alertCount }}
            </span>
            @endif
        </button>
    </div>

    <!-- Alerts Dropdown -->
    @if($showAlerts)
    <div class="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-y-auto">
        <!-- Header -->
        <div class="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">
                System Alerts
                @if($alertCount > 0)
                <span class="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">{{ $alertCount }}</span>
                @endif
            </h3>
            <div class="flex space-x-2">
                <button wire:click="refreshAlerts" 
                        class="text-gray-400 hover:text-gray-600 transition-colors"
                        title="Refresh alerts">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
                <button wire:click="toggleAlerts" 
                        class="text-gray-400 hover:text-gray-600 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Alerts List -->
        <div class="max-h-80 overflow-y-auto">
            @if(empty($alerts))
            <div class="p-6 text-center text-gray-500">
                <svg class="w-12 h-12 mx-auto mb-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p class="text-sm">No active alerts</p>
                <p class="text-xs text-gray-400 mt-1">All systems operating normally</p>
            </div>
            @else
                @foreach($alerts as $index => $alert)
                <div class="p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <!-- Alert Header -->
                            <div class="flex items-center mb-2">
                                <!-- Severity Icon -->
                                @if($alert['severity'] === 'critical')
                                <div class="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mr-3 animate-pulse"></div>
                                @elseif($alert['severity'] === 'high')
                                <div class="flex-shrink-0 w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                                @elseif($alert['severity'] === 'medium')
                                <div class="flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                                @else
                                <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                @endif

                                <h4 class="text-sm font-medium text-gray-900">{{ $alert['title'] }}</h4>
                                
                                <!-- Severity Badge -->
                                <span class="ml-2 px-2 py-1 text-xs rounded-full {{ $alert['severity'] === 'critical' ? 'bg-red-100 text-red-800' : ($alert['severity'] === 'high' ? 'bg-orange-100 text-orange-800' : ($alert['severity'] === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800')) }}">
                                    {{ ucfirst($alert['severity']) }}
                                </span>
                            </div>

                            <!-- Alert Message -->
                            <p class="text-sm text-gray-600 mb-2">{{ $alert['message'] }}</p>

                            <!-- Timestamp -->
                            <p class="text-xs text-gray-400 mb-3">
                                {{ \Carbon\Carbon::parse($alert['timestamp'])->diffForHumans() }}
                            </p>

                            <!-- Actions -->
                            @if(!empty($alert['actions']))
                            <div class="flex flex-wrap gap-2">
                                @foreach($alert['actions'] as $action)
                                <button wire:click="{{ $action['action'] }}({{ isset($action['params']) ? json_encode($action['params']) : '' }})"
                                        class="px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 rounded transition-colors">
                                    {{ $action['label'] }}
                                </button>
                                @endforeach
                            </div>
                            @endif
                        </div>

                        <!-- Dismiss Button -->
                        <button wire:click="dismissAlert({{ $index }})" 
                                class="ml-3 text-gray-400 hover:text-gray-600 transition-colors"
                                title="Dismiss alert">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                @endforeach
            @endif
        </div>

        <!-- Footer -->
        @if(!empty($alerts))
        <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
            <div class="flex justify-between items-center">
                <p class="text-xs text-gray-500">
                    {{ count($alerts) }} active alert{{ count($alerts) !== 1 ? 's' : '' }}
                </p>
                <a href="{{ route('error-accounts') }}" 
                   class="text-xs text-blue-600 hover:text-blue-800 transition-colors">
                    View Error Dashboard →
                </a>
            </div>
        </div>
        @endif
    </div>
    @endif
</div>

@push('scripts')
<script>
    // Auto-refresh alerts every 30 seconds
    setInterval(() => {
        @this.call('refreshAlerts');
    }, 30000);

    // Listen for real-time error events
    window.addEventListener('error.created', event => {
        // Refresh alerts when new errors are created
        @this.call('refreshAlerts');
    });

    window.addEventListener('error.resolved', event => {
        // Refresh alerts when errors are resolved
        @this.call('refreshAlerts');
    });

    // Handle alert actions
    window.addEventListener('view-error', event => {
        // Navigate to error details or open modal
        console.log('View error:', event.detail.errorId);
    });

    window.addEventListener('resolve-error', event => {
        // Resolve error
        console.log('Resolve error:', event.detail.errorId);
    });

    window.addEventListener('filter-by-severity', event => {
        // Apply severity filter to error accounts page
        console.log('Filter by severity:', event.detail.severity);
    });

    window.addEventListener('filter-by-category', event => {
        // Apply category filter to error accounts page
        console.log('Filter by category:', event.detail.category);
    });

    window.addEventListener('filter-by-vps', event => {
        // Apply VPS filter to error accounts page
        console.log('Filter by VPS:', event.detail.vpsId);
    });
</script>
@endpush
