<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-6 space-y-8">
        {{-- Page Header --}}
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
            <div class="flex items-center gap-2">
                <x-ui.tooltip text="Live updates every 10 seconds">
                    <x-ui.badge variant="info" size="sm">Live</x-ui.badge>
                </x-ui.tooltip>
                <x-ui.button 
                    wire:click="$refresh"
                    wire:loading.attr="disabled"
                    variant="secondary"
                    size="sm">
                    <div wire:loading.remove>
                        Refresh All
                    </div>
                    <x-ui.loading-state type="inline" show wire:loading />
                </x-ui.button>
            </div>
        </div>

        {{-- Command Metrics --}}
        <div class="relative">
            <x-ui.loading-state type="overlay" show="true" wire:loading.delay wire:target="$refresh" message="Loading metrics..." />
            <x-ui.card>
                <x-slot name="header">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">Command Metrics</h2>
                        <x-ui.tooltip text="Statistics for the last 24 hours">
                            <x-ui.badge variant="info" size="sm">24h</x-ui.badge>
                        </x-ui.tooltip>
                    </div>
                </x-slot>
                @livewire('command-metrics', key('command-metrics-' . now()->timestamp))
            </x-ui.card>
        </div>

        {{-- Main Grid --}}
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {{-- Command List --}}
            <div class="lg:col-span-1 space-y-6">
                <div class="relative">
                    <x-ui.loading-state type="overlay" show="true" wire:loading.delay wire:target="$refresh">
                        Loading commands...
                    </x-ui.loading-state>
                    <x-ui.card>
                        <x-slot name="header">
                            <div class="flex items-center justify-between">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Recent Commands</h2>
                                <x-ui.tooltip text="Live updates every 10 seconds">
                                    <x-ui.badge variant="info" size="sm">Live</x-ui.badge>
                                </x-ui.tooltip>
                            </div>
                        </x-slot>
                        @livewire('command-list', key('command-list'))
                    </x-ui.card>
                </div>
            </div>

            {{-- Missing Accounts --}}
            <div class="lg:col-span-1 space-y-6">
                <div class="relative">
                    <x-ui.loading-state type="overlay" show="true" wire:loading.delay wire:target="$refresh">
                        Loading accounts...
                    </x-ui.loading-state>
                    <x-ui.card>
                        <x-slot name="header">
                            <div class="flex items-center justify-between">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Missing Accounts</h2>
                                <x-ui.tooltip text="Live updates every 10 seconds">
                                    <x-ui.badge variant="info" size="sm">Live</x-ui.badge>
                                </x-ui.tooltip>
                            </div>
                        </x-slot>
                        @livewire('missing-accounts', key('missing-accounts-' . now()->timestamp))
                    </x-ui.card>
                </div>
            </div>

            {{-- Error Accounts --}}
            <div class="lg:col-span-1 space-y-6">
                <div class="relative">
                    <x-ui.loading-state type="overlay" show="true" wire:loading.delay wire:target="$refresh">
                        Loading accounts...
                    </x-ui.loading-state>
                    <x-ui.card>
                        <x-slot name="header">
                            <div class="flex items-center justify-between">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Error Accounts</h2>
                                <x-ui.tooltip text="Live updates every 10 seconds">
                                    <x-ui.badge variant="info" size="sm">Live</x-ui.badge>
                                </x-ui.tooltip>
                            </div>
                        </x-slot>
                        @livewire('enhanced-error-accounts', key('enhanced-error-accounts-' . now()->timestamp))
                    </x-ui.card>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('livewire:init', () => {
    // Handle flash messages
    Livewire.on('flash', (event) => {
        window.dispatchEvent(new CustomEvent('notify', { 
            detail: { message: event.message, type: event.type } 
        }));
    });

    // Handle errors
    Livewire.on('error', (message) => {
        window.dispatchEvent(new CustomEvent('notify', { 
            detail: { message: message, type: 'error' }
        }));
    });

    // Handle refresh completion
    Livewire.on('refreshCompleted', () => {
        window.dispatchEvent(new CustomEvent('notify', { 
            detail: { message: 'Dashboard refreshed successfully', type: 'success' }
        }));
    });
});
</script>
@endpush
