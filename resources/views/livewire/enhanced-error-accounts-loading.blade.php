<div class="space-y-6">
    <!-- Header Skeleton -->
    <div class="flex justify-between items-center">
        <div class="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
        <div class="flex space-x-2">
            <div class="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
            <div class="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
    </div>

    <!-- Summary Cards Skeleton -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        @for($i = 0; $i < 5; $i++)
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-gray-200 rounded-lg animate-pulse">
                    <div class="w-6 h-6 bg-gray-300 rounded"></div>
                </div>
                <div class="ml-4 flex-1">
                    <div class="h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"></div>
                    <div class="h-8 bg-gray-200 rounded w-12 animate-pulse"></div>
                </div>
            </div>
        </div>
        @endfor
    </div>

    <!-- Main Content Skeleton -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            @for($i = 0; $i < 6; $i++)
            <div class="border border-gray-200 rounded-lg p-4">
                <!-- Account Header Skeleton -->
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-gray-200 rounded mr-3 animate-pulse"></div>
                        <div>
                            <div class="h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"></div>
                            <div class="h-3 bg-gray-200 rounded w-32 animate-pulse"></div>
                        </div>
                    </div>
                    <div class="h-6 bg-gray-200 rounded w-16 animate-pulse"></div>
                </div>

                <!-- Error Details Skeleton -->
                <div class="space-y-2 mb-4">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-gray-200 rounded mr-2 animate-pulse"></div>
                        <div class="h-3 bg-gray-200 rounded w-40 animate-pulse"></div>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-gray-200 rounded mr-2 animate-pulse"></div>
                        <div class="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-gray-200 rounded mr-2 animate-pulse"></div>
                        <div class="h-3 bg-gray-200 rounded w-20 animate-pulse"></div>
                    </div>
                </div>

                <!-- Categories Skeleton -->
                <div class="flex flex-wrap gap-1 mb-4">
                    <div class="h-5 bg-gray-200 rounded-full w-16 animate-pulse"></div>
                    <div class="h-5 bg-gray-200 rounded-full w-20 animate-pulse"></div>
                </div>

                <!-- Actions Skeleton -->
                <div class="flex justify-between items-center">
                    <div class="flex space-x-2">
                        <div class="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                        <div class="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                    </div>
                    <div class="h-6 bg-gray-200 rounded w-16 animate-pulse"></div>
                </div>
            </div>
            @endfor
        </div>
    </div>

    <!-- Loading Message -->
    <div class="text-center py-8">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-600">Loading comprehensive error analysis...</p>
        <p class="text-sm text-gray-500 mt-2">Analyzing error patterns across VPS and accounts</p>
    </div>
</div>
