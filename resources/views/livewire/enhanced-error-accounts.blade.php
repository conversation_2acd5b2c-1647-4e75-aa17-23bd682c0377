<div class="space-y-6">
    <!-- Header with Summary Cards -->
    <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
            <h2 class="text-2xl font-bold text-gray-900">Error Accounts Dashboard</h2>
            @if($readyToLoad && isset($errorSummary))
                <!-- Live Status Indicator -->
                <div class="flex items-center space-x-2">
                    @if(($errorSummary['by_severity']['critical'] ?? 0) > 0)
                        <div class="flex items-center px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">
                            <div class="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></div>
                            {{ $errorSummary['by_severity']['critical'] }} Critical
                        </div>
                    @elseif(($errorSummary['total_errors'] ?? 0) > 0)
                        <div class="flex items-center px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">
                            <div class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                            {{ $errorSummary['total_errors'] }} Active
                        </div>
                    @else
                        <div class="flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            All Clear
                        </div>
                    @endif
                </div>
            @endif
        </div>
        <div class="flex items-center space-x-2">
            <button wire:click="toggleFilters"
                    class="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
                <i class="fas fa-filter mr-2"></i>
                {{ $showFilters ? 'Hide Filters' : 'Show Filters' }}
            </button>
            <button wire:click="exportErrorReport"
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                <i class="fas fa-download mr-2"></i>
                Export Report
            </button>
            <!-- Test Toast Button (remove in production) -->
            <button onclick="showToast('🚨 Test critical error alert!', 'critical')"
                    class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors text-sm">
                Test Alert
            </button>
        </div>
    </div>

    <!-- Summary Cards -->
    @if($readyToLoad && isset($errorSummary))
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 min-w-0">
            <div class="flex items-center">
                <div class="flex-shrink-0 p-3 bg-red-100 rounded-lg">
                    <i class="fas fa-exclamation-triangle text-red-600 text-lg"></i>
                </div>
                <div class="ml-4 min-w-0 flex-1">
                    <p class="text-sm font-medium text-gray-600 truncate">Total Errors</p>
                    <p class="text-3xl font-bold text-gray-900">{{ number_format($errorSummary['total_errors'] ?? 0) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 min-w-0">
            <div class="flex items-center">
                <div class="flex-shrink-0 p-3 bg-orange-100 rounded-lg">
                    <i class="fas fa-users text-orange-600 text-lg"></i>
                </div>
                <div class="ml-4 min-w-0 flex-1">
                    <p class="text-sm font-medium text-gray-600 truncate">Affected Accounts</p>
                    <p class="text-3xl font-bold text-gray-900">{{ number_format($errorSummary['affected_accounts'] ?? 0) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 min-w-0">
            <div class="flex items-center">
                <div class="flex-shrink-0 p-3 bg-purple-100 rounded-lg">
                    <i class="fas fa-server text-purple-600 text-lg"></i>
                </div>
                <div class="ml-4 min-w-0 flex-1">
                    <p class="text-sm font-medium text-gray-600 truncate">Affected VPS</p>
                    <p class="text-3xl font-bold text-gray-900">{{ number_format($errorSummary['affected_vps'] ?? 0) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 min-w-0">
            <div class="flex items-center">
                <div class="flex-shrink-0 p-3 bg-red-100 rounded-lg">
                    <i class="fas fa-fire text-red-600 text-lg"></i>
                </div>
                <div class="ml-4 min-w-0 flex-1">
                    <p class="text-sm font-medium text-gray-600 truncate">Critical Errors</p>
                    <p class="text-3xl font-bold text-gray-900">{{ number_format($errorSummary['by_severity']['critical'] ?? 0) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 min-w-0">
            <div class="flex items-center">
                <div class="flex-shrink-0 p-3 bg-yellow-100 rounded-lg">
                    <i class="fas fa-shield-alt text-yellow-600 text-lg"></i>
                </div>
                <div class="ml-4 min-w-0 flex-1">
                    <p class="text-sm font-medium text-gray-600 truncate">Auth Errors</p>
                    <p class="text-3xl font-bold text-gray-900">{{ number_format($errorSummary['by_category']['authentication'] ?? 0) }}</p>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Filters Panel -->
    @if($showFilters)
    <div class="bg-white rounded-lg shadow p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <!-- Category Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select wire:model.live="filterCategory" class="w-full rounded-md border-gray-300 shadow-sm">
                    @foreach($this->getErrorCategoryOptions() as $value => $label)
                        <option value="{{ $value }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Severity Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Severity</label>
                <select wire:model.live="filterSeverity" class="w-full rounded-md border-gray-300 shadow-sm">
                    @foreach($this->getSeverityOptions() as $value => $label)
                        <option value="{{ $value }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>

            <!-- VPS Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">VPS</label>
                <select wire:model.live="filterVpsId" class="w-full rounded-md border-gray-300 shadow-sm">
                    <option value="all">All VPS</option>
                    @if(isset($vpsList))
                        @foreach($vpsList as $vps)
                            <option value="{{ $vps->id }}">{{ $vps->name }}</option>
                        @endforeach
                    @endif
                </select>
            </div>

            <!-- Time Range Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Time Range</label>
                <select wire:model.live="timeRange" class="w-full rounded-md border-gray-300 shadow-sm">
                    @foreach($this->getTimeRangeOptions() as $value => $label)
                        <option value="{{ $value }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Group By -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Group By</label>
                <select wire:model.live="groupBy" class="w-full rounded-md border-gray-300 shadow-sm">
                    @foreach($this->getGroupByOptions() as $value => $label)
                        <option value="{{ $value }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Search -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input type="text" 
                       wire:model.live.debounce.300ms="search" 
                       placeholder="Account, email, error..."
                       class="w-full rounded-md border-gray-300 shadow-sm">
            </div>
        </div>

        <div class="mt-4 flex justify-between items-center">
            <button wire:click="resetFilters" 
                    class="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
                <i class="fas fa-undo mr-2"></i>
                Reset Filters
            </button>

            @if(!empty($selectedAccounts))
            <div class="flex space-x-2">
                <span class="text-sm text-gray-600">{{ count($selectedAccounts) }} selected</span>
                <button wire:click="bulkResolveErrors" 
                        class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                    <i class="fas fa-check mr-2"></i>
                    Bulk Resolve
                </button>
            </div>
            @endif
        </div>
    </div>
    @endif

    <!-- Loading State -->
    @if(!$readyToLoad)
    <div class="bg-white rounded-lg shadow p-8 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p class="mt-4 text-gray-600">Loading error accounts...</p>
    </div>
    @endif

    <!-- Error Accounts Content -->
    @if($readyToLoad && isset($errorAccounts))
        @if($errorAccounts->isEmpty())
        <div class="bg-white rounded-lg shadow p-8 text-center">
            <div class="text-gray-400 mb-4">
                <i class="fas fa-check-circle text-6xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Error Accounts Found</h3>
            <p class="text-gray-600">All accounts are functioning normally with the current filters.</p>
        </div>
        @else
            @foreach($errorAccounts as $groupName => $accounts)
            <div class="bg-white rounded-lg shadow">
                @if($groupBy !== 'none')
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h3 class="text-lg font-medium text-gray-900 capitalize">
                        {{ str_replace('_', ' ', $groupName) }}
                        <span class="text-sm text-gray-500 ml-2">({{ $accounts->count() }} accounts)</span>
                    </h3>
                </div>
                @endif

                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                        @foreach($accounts as $account)
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <!-- Account Header -->
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <input type="checkbox" 
                                           wire:model="selectedAccounts" 
                                           value="{{ $account->id }}"
                                           class="rounded border-gray-300 text-blue-600 mr-3">
                                    <div>
                                        <h4 class="font-medium text-gray-900">{{ $account->login_name ?? 'Unknown' }}</h4>
                                        <p class="text-sm text-gray-500">{{ $account->registered_email ?? 'No email' }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($account->latest_error->severity === 'critical')
                                        <span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">Critical</span>
                                    @elseif($account->latest_error->severity === 'high')
                                        <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">High</span>
                                    @elseif($account->latest_error->severity === 'medium')
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">Medium</span>
                                    @else
                                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">Low</span>
                                    @endif
                                </div>
                            </div>

                            <!-- Error Details -->
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                                    <span class="text-gray-600">{{ $account->latest_error->error_message }}</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-clock text-gray-400 mr-2"></i>
                                    <span class="text-gray-500">{{ $account->latest_error->occurred_at->diffForHumans() }}</span>
                                </div>
                                @if($account->latest_error->vps)
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-server text-gray-400 mr-2"></i>
                                    <span class="text-gray-500">{{ $account->latest_error->vps->name }}</span>
                                </div>
                                @endif
                            </div>

                            <!-- Error Categories -->
                            <div class="flex flex-wrap gap-1 mb-4">
                                @foreach($account->error_categories as $category)
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                    {{ ucfirst(str_replace('_', ' ', $category)) }}
                                </span>
                                @endforeach
                            </div>

                            <!-- Actions -->
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2">
                                    @if($account->latest_error->screenshot_url)
                                    <a href="{{ $account->latest_error->screenshot_url }}" 
                                       target="_blank"
                                       class="text-blue-600 hover:text-blue-800 text-sm">
                                        <i class="fas fa-image mr-1"></i>
                                        Screenshot
                                    </a>
                                    @endif
                                    <button wire:click="retryAccountLogin({{ $account->id }})"
                                            class="text-green-600 hover:text-green-800 text-sm">
                                        <i class="fas fa-redo mr-1"></i>
                                        Retry Login
                                    </button>
                                </div>
                                <button wire:click="resolveError({{ $account->latest_error->id }})"
                                        class="px-3 py-1 bg-green-100 hover:bg-green-200 text-green-800 text-sm rounded transition-colors">
                                    <i class="fas fa-check mr-1"></i>
                                    Resolve
                                </button>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endforeach
        @endif
    @endif
</div>

@push('scripts')
<script>
    // Real-time updates via WebSocket/Broadcasting
    if (typeof Echo !== 'undefined') {
        Echo.channel('error-accounts')
            .listen('.error.created', (e) => {
                // Show appropriate toast based on severity
                const severity = e.severity || 'medium';
                const toastType = severity === 'critical' ? 'critical' :
                                 severity === 'high' ? 'error' : 'warning';

                if (severity === 'critical') {
                    showCriticalAlert(e);
                } else {
                    showToast(
                        `New ${severity} error: ${e.error_message}`,
                        toastType,
                        severity === 'high' ? 8000 : 5000
                    );
                }

                // Refresh the component data
                @this.call('refreshData');
            })
            .listen('.error.resolved', (e) => {
                // Show success notification
                showToast('✅ Error resolved for account: ' + (e.account?.login_name || 'Unknown'), 'success');
                // Refresh the component data
                @this.call('refreshData');
            });
    }

    // Fallback: Poll for updates every 30 seconds
    setInterval(() => {
        @this.call('refreshData');
    }, 30000);

    // Enhanced Toast notification system
    let toastContainer = null;
    let toastCount = 0;

    function createToastContainer() {
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2';
            toastContainer.style.pointerEvents = 'none';
            document.body.appendChild(toastContainer);
        }
        return toastContainer;
    }

    function showToast(message, type = 'info', duration = 5000) {
        const container = createToastContainer();
        toastCount++;

        const toast = document.createElement('div');
        toast.className = `px-6 py-4 rounded-lg shadow-lg text-white transition-all duration-300 transform translate-x-full opacity-0 max-w-sm ${
            type === 'success' ? 'bg-green-500' :
            type === 'warning' ? 'bg-yellow-500' :
            type === 'error' ? 'bg-red-500' :
            type === 'critical' ? 'bg-red-600 border-2 border-red-300' : 'bg-blue-500'
        }`;
        toast.style.pointerEvents = 'auto';

        // Create toast content
        const content = document.createElement('div');
        content.className = 'flex items-center';

        const icon = document.createElement('i');
        icon.className = `fas mr-3 ${
            type === 'success' ? 'fa-check-circle' :
            type === 'warning' ? 'fa-exclamation-triangle' :
            type === 'error' || type === 'critical' ? 'fa-exclamation-circle' : 'fa-info-circle'
        }`;

        const text = document.createElement('span');
        text.textContent = message;
        text.className = 'flex-1';

        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.className = 'ml-3 text-white hover:text-gray-200 transition-colors';
        closeBtn.onclick = () => removeToast(toast);

        content.appendChild(icon);
        content.appendChild(text);
        content.appendChild(closeBtn);
        toast.appendChild(content);

        container.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
        }, 10);

        // Auto-remove
        const timeout = setTimeout(() => {
            removeToast(toast);
        }, duration);

        // Store timeout for manual removal
        toast._timeout = timeout;

        return toast;
    }

    function removeToast(toast) {
        if (toast._timeout) {
            clearTimeout(toast._timeout);
        }

        toast.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
                toastCount--;

                // Remove container if empty
                if (toastCount === 0 && toastContainer) {
                    document.body.removeChild(toastContainer);
                    toastContainer = null;
                }
            }
        }, 300);
    }

    // Show critical error alerts immediately
    function showCriticalAlert(errorData) {
        showToast(
            `🚨 Critical Error: ${errorData.error_message}`,
            'critical',
            10000 // 10 seconds for critical errors
        );
    }

    // Event handlers
    window.addEventListener('error-resolved', event => {
        showToast('Error resolved successfully', 'success');
    });

    window.addEventListener('bulk-errors-resolved', event => {
        showToast('Multiple errors resolved successfully', 'success');
    });

    window.addEventListener('retry-account-login', event => {
        showToast('Login retry initiated for account', 'info');
    });

    window.addEventListener('export-error-report', event => {
        showToast('Error report export started', 'info');
    });

    window.addEventListener('error-account-updated', event => {
        const { type, data } = event.detail;
        if (type === 'new') {
            const severity = data.severity || 'medium';
            const toastType = severity === 'critical' ? 'critical' :
                             severity === 'high' ? 'error' : 'warning';

            if (severity === 'critical') {
                showCriticalAlert(data);
            } else {
                showToast(
                    `📢 New ${severity} error: ${data.error_message}`,
                    toastType,
                    severity === 'high' ? 8000 : 5000
                );
            }
        } else if (type === 'resolved') {
            showToast('✅ Error resolved successfully', 'success');
        }
    });

    // Filter integration with alerts
    window.addEventListener('filter-by-severity', event => {
        @this.set('filterSeverity', event.detail.severity);
        @this.set('showFilters', true);
    });

    window.addEventListener('filter-by-category', event => {
        @this.set('filterCategory', event.detail.category);
        @this.set('showFilters', true);
    });

    window.addEventListener('filter-by-vps', event => {
        @this.set('filterVpsId', event.detail.vpsId);
        @this.set('showFilters', true);
    });
</script>
@endpush
